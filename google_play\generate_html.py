import json
import os
from datetime import datetime
import re

def load_config(config_path):
    with open(config_path, "r", encoding="utf-8") as f:
        return json.load(f)

def load_history(history_path):
    with open(history_path, "r", encoding="utf-8") as f:
        return json.load(f)

def parse_date(date_str):
    if not date_str:
        return None
    try:
        # 处理 "2020-05-17" 格式
        if re.match(r"\d{4}-\d{2}-\d{2}$", date_str):
            return datetime.strptime(date_str, "%Y-%m-%d")
        # 处理 "2024-07-08T07:00:00Z" 格式
        elif "T" in date_str:
            return datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%SZ")
        # 处理旧的 "May 14, 2025" 格式
        else:
            return datetime.strptime(date_str, "%b %d, %Y")
    except:
        return None

def date_to_color(release_date):
    if not release_date:
        return "#000000"
    delta = datetime.now() - release_date
    if delta.days > 180:
        return "#000000"
    intensity = 255 - int((delta.days / 180) * 200)
    return f"rgb(0,0,{intensity})"

def get_download_font_size(download_str):
    try:
        num = int(''.join(filter(str.isdigit, download_str or '')))
        if num >= 100000000: return "20px"
        if num >= 10000000:  return "18px"
        if num >= 1000000:   return "16px"
        if num >= 100000:    return "14px"
        if num >= 10000:     return "13px"
        return "12px"
    except:
        return "12px"

def generate_filtered_html(config, history, output_file):
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    dev_prefix = config["developer_url_prefix"]
    app_prefix = config["app_url_prefix"]

    total_games = sum(len(apps) for apps in history.values())

    html_parts = [
        '<!DOCTYPE html><html><head><meta charset="utf-8">',
        '<meta name="viewport" content="width=device-width, initial-scale=1">',
        '<title>游戏列表</title>',
        '<style>',
        'body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif; margin: 10px; font-size: 15px; }',
        'details { margin-bottom: 10px; }',
        '.app-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)); gap: 10px; }',
        '.app-item { padding: 10px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px; background: #fff; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }',
        '.app-item a { font-weight: bold; display: block; margin-bottom: 5px; color: #000000; }',
        '.app-date { font-size: 12px; color: #666; }',
        '.vendor-header { font-size: 16px; font-weight: bold; margin-top: 20px; }',
        '.filter-buttons { display: flex; flex-wrap: wrap; gap: 6px; margin-bottom: 10px; }',
        '.filter-button { padding: 6px 12px; font-size: 14px; border: none; border-radius: 6px; background: #007bff; color: white; cursor: pointer; }',
        '.filter-button.active { background: #0056b3; }',
        '</style>',
        '</head><body>',
        f'<h1>🎮 新游戏 （{datetime.now().strftime("%Y/%m/%d")}）</h1>',
        '<div class="filter-buttons" id="day-buttons"></div>',
        '<div id="recent-apps"></div>',
        f'<hr><h1>📦 游戏列表（ {total_games} 个游戏）</h1>'
    ]

    for filename, apps in history.items():
        vendor = os.path.splitext(filename)[0]
        if vendor.startswith("apple_"):
            vendorLst = vendor.split("_")
            vendor_url = f"{dev_prefix}/{vendorLst[1]}/{vendorLst[2]}"
            vendor_display = vendorLst[1]
        else:
            vendor_url = dev_prefix + vendor
            vendor_display = vendor

        sorted_apps = sorted(
            apps.items(),
            key=lambda x: parse_date(x[1].get("Released on", "")) or datetime.min,
            reverse=True
        )

        html_parts.append(f'<details><summary><strong><a href="{vendor_url}" target="_blank">{vendor_display}</a></strong> ({len(sorted_apps)} 个游戏)</summary><div class="app-grid">')
        for package, info in sorted_apps:
            name = info.get("name", "未知")
            released = parse_date(info.get("Released on", ""))
            released_fmt = released.strftime("%Y/%m/%d") if released else "未知"
            updated = parse_date(info.get("Updated on", ""))
            updated_fmt = updated.strftime("%Y/%m/%d") if updated else "未知"
            downloads_raw = info.get("Downloads") or ""
            downloads = downloads_raw.replace("downloads", "").strip()
            stat = info.get("Stat", "")
            ratings = info.get("Ratings", "")

            if vendor.startswith("apple_"):
                app_url = f"{app_prefix}/{info['name']}/{package}"
            else:
                app_url = app_prefix + package

            downloads_html = f'<div class="app-date" style="font-size:{get_download_font_size(downloads)}">下载: {downloads}</div>' if downloads else ''
            stat_html = f'<div class="app-date">评分: {stat}</div>' if stat else ''
            ratings_html = f'<div class="app-date">评论数: {ratings}</div>' if ratings else ''

            html_parts.append(
                f'<div class="app-item">'
                f'<a href="{app_url}" target="_blank">{name}</a>'
                f'<div class="app-date" style="color:{date_to_color(released)}">发布: {released_fmt}</div>'
                f'<div class="app-date" style="color:{date_to_color(updated)}">更新: {updated_fmt}</div>'
                f'{downloads_html}'
                f'{stat_html}{ratings_html}'
                f'</div>'
            )
        html_parts.append('</div></details>')

    html_script = f"""
<script>
const apps = {json.dumps(history, ensure_ascii=False)};
const appPrefix = '{app_prefix}';
const devPrefix = '{dev_prefix}';
const today = new Date();
const dayOptions = [7, 30, 60, 90, 120, 365];

function parseDate(str) {{
  if (!str || typeof str !== "string") return null;
  
  // 处理 "2020-05-17" 格式
  if (/^\d{{4}}-\d{{2}}-\d{{2}}$/.test(str)) {{
    const [year, month, day] = str.split('-').map(Number);
    return new Date(year, month - 1, day);
  }}
  
  // 处理 "2024-07-08T07:00:00Z" 格式
  if (str.includes('T')) {{
    return new Date(str);
  }}
  
  // 处理旧的 "May 14, 2025" 格式
  const parts = str.split(" ");
  const months = {{"Jan":0,"Feb":1,"Mar":2,"Apr":3,"May":4,"Jun":5,"Jul":6,"Aug":7,"Sep":8,"Oct":9,"Nov":10,"Dec":11}};
  if (parts.length !== 3 || !(parts[0] in months)) return null;
  return new Date(parseInt(parts[2]), months[parts[0]], parseInt(parts[1]));
}}

function dateToColor(str) {{
  const d = parseDate(str);
  if (!d) return "#000000";
  const now = new Date();
  const days = Math.floor((now - d) / (1000 * 60 * 60 * 24));
  if (days > 180) return "#000000";
  const intensity = 255 - Math.floor((days / 180) * 200);
  return `rgb(0,0,${{intensity}})`;
}}

function getDownloadFontSize(downloadStr) {{
  const num = parseInt((downloadStr || "").replace(/[^0-9]/g, ""));
  if (num >= 100000000) return "20px";
  if (num >= 10000000)  return "18px";
  if (num >= 1000000)   return "16px";
  if (num >= 100000)    return "14px";
  if (num >= 10000)     return "13px";
  return "12px";
}}

function formatDate(str) {{
  const d = parseDate(str);
  if (!d) return "未知";
  return d.getFullYear() + "/" + (d.getMonth() + 1).toString().padStart(2, '0') + "/" + d.getDate().toString().padStart(2, '0');
}}

function cleanDownloads(str) {{
  return (str || "").replace(/\s*downloads/i, "").trim();
}}

function createDayButtons() {{
  const container = document.getElementById("day-buttons");
  dayOptions.forEach(d => {{
    const btn = document.createElement("button");
    btn.className = "filter-button";
    btn.textContent = `${{d}}天`;
    btn.onclick = () => {{
      document.querySelectorAll(".filter-button").forEach(b => b.classList.remove("active"));
      btn.classList.add("active");
      filterDays(d);
    }};
    if (d === 3) btn.classList.add("active");
    container.appendChild(btn);
  }});
}}

function filterDays(days) {{
  const cutoff = new Date(today.getTime() - days * 24 * 60 * 60 * 1000);
  const container = document.getElementById("recent-apps");
  container.innerHTML = "";

  for (const [vendorFile, appsMap] of Object.entries(apps)) {{
    let vendor = vendorFile.replace(/\.html$/, "");
    let vendorUrl
    if (vendor.startsWith("apple_")) {{
      vendorUrl = devPrefix + vendor.split("_")[1] +"//"+vendor.split("_")[2];
      vendor = vendor.split("_")[1] 
    }} else {{
      vendorUrl = devPrefix + vendor;
    }}
    
    let found = [];

    for (const [pkg, info] of Object.entries(appsMap)) {{
      const relStr = info["Released on"];
      const relDate = parseDate(relStr);
      if (relDate && relDate >= cutoff) {{
        found.push({{
          name: info["name"],
          pkg,
          released: relStr,
          updated: info["Updated on"] || "未知",
          downloads: info["Downloads"] || "",
          stat: info["Stat"] || "",
          ratings: info["Ratings"] || ""
        }});
      }}
    }}

    if (found.length > 0) {{
      let html = `<div class='vendor-header'><a href='${{vendorUrl}}' target='_blank'>${{vendor}}</a></div><div class="app-grid">`;
      for (const app of found.sort((a, b) => {{
        const da = parseDate(a.released);
        const db = parseDate(b.released);
        if (da && db) return db - da;
        if (da) return -1;
        if (db) return 1;
        return a.name.localeCompare(b.name);
      }})) {{
        let downloadsHtml = app.downloads ? `<div class="app-date" style="font-size:${{getDownloadFontSize(app.downloads)}}">下载: ${{cleanDownloads(app.downloads)}}</div>` : '';
        let statHtml = app.stat ? `<div class="app-date">评分: ${{app.stat}}</div>` : '';
        let ratingsHtml = app.ratings ? `<div class="app-date">评论数: ${{app.ratings}}</div>` : '';
        html += `<div class="app-item">
                  <a href="${{appPrefix + app.pkg}}" target="_blank" style="color:#000000">${{app.name}}</a>
                  <div class="app-date" style="color:${{dateToColor(app.released)}}">发布: ${{formatDate(app.released)}}</div>
                  <div class="app-date" style="color:${{dateToColor(app.updated)}}">更新: ${{formatDate(app.updated)}}</div>
                  ${{downloadsHtml}}
                  ${{statHtml}}
                  ${{ratingsHtml}}
                </div>`;
      }}
      html += "</div>";
      container.innerHTML += html;
    }}
  }}
  if (container.innerHTML === "") {{
    container.innerHTML = `<p>🎯 当前选择的时间范围（${{days}}天）内，没有发布新游戏。</p>`;
  }}
}}

window.onload = () => {{
  createDayButtons();
  filterDays(7);
}};
</script>
</body></html>
"""

    html_parts.append(html_script)

    with open(output_file, "w", encoding="utf-8") as f:
        f.write('\n'.join(html_parts))

def main():
    import sys
    if len(sys.argv) >= 2:
        config_path = sys.argv[1]
    else:
        script_name = os.path.splitext(os.path.basename(sys.argv[0]))[0]
        config_path = f"{script_name}.cfg"

    config = load_config(config_path)
    history_path = config["data_file"]
    output_path = config["output_file"]

    history = load_history(history_path)
    generate_filtered_html(config, history, output_path)
    print(f"✅ HTML 已生成: {output_path}")

if __name__ == "__main__":
    main()