// 解析JSON数据
let appsData = JSON.parse(__APPS__);
const APP_PREFIX = __APP_PREFIX__;
const DEV_PREFIX = __DEV_PREFIX__;
const MAIN_VENDORS = JSON.parse(__MAIN_VENDORS__);

// 分页配置
const ROWS_PER_PAGE = 6; // 每页显示6行
let currentPage = 1;
let filteredApps = [];
let isLoading = false;
let itemsPerRow = 9; // 默认值，会在运行时动态计算

// 视图模式
let currentViewMode = 'icon';
let selectedReleaseDays = 7; // 默认选中7天
let selectedUpdateDays = null;
let selectedTags = new Set();
let selectedVendors = new Set();
let nameFilter = '';

// 初始化函数
function init() {
    setupEventListeners();
    processInitialData();
    
    // 设置默认的厂商选择
    const allVendors = new Set(MAIN_VENDORS);
    allVendors.forEach(vendor => {
        if (vendor !== "其他") {
            selectedVendors.add(vendor);
        }
    });
    
    renderFilters();
    calculateItemsPerRow();
    loadAndRenderApps(true);
    updateStats();

    // 监听窗口大小变化
    window.addEventListener('resize', debounce(() => {
        const oldItemsPerRow = itemsPerRow;
        calculateItemsPerRow();
        // 只有当每行显示数量改变时才重新渲染
        if (oldItemsPerRow !== itemsPerRow) {
            loadAndRenderApps(true);
        }
    }, 250));
}

// 设置事件监听器
function setupEventListeners() {
    document.querySelectorAll('.view-mode-button').forEach(button => {
        button.addEventListener('click', () => {
            currentViewMode = button.dataset.mode;
            document.querySelectorAll('.view-mode-button').forEach(b => b.classList.remove('active'));
            button.classList.add('active');
            currentPage = 1;
            loadAndRenderApps(true);
        });
    });

    const filterToggle = document.getElementById('filter-toggle');
    const filterPanel = document.getElementById('filter-panel');
    const filterClose = document.querySelector('.filter-close');
    const filterApply = document.querySelector('.filter-apply');

    filterToggle.addEventListener('click', () => filterPanel.classList.add('active'));
    filterClose.addEventListener('click', () => filterPanel.classList.remove('active'));
    filterApply.addEventListener('click', () => {
        filterPanel.classList.remove('active');
        currentPage = 1;
        loadAndRenderApps(true);
    });

    const loadMoreButton = document.getElementById('load-more');
    loadMoreButton.addEventListener('click', () => {
        if (!isLoading) {
            currentPage++;
            loadAndRenderApps(false);
        }
    });

    // 添加滚动事件监听
    window.addEventListener('scroll', scrollHandler);

    // 添加名称筛选输入框事件监听
    const nameFilterInput = document.getElementById('name-filter');
    nameFilterInput.addEventListener('input', debounce(() => {
        nameFilter = nameFilterInput.value.toLowerCase().trim();
        currentPage = 1;
        loadAndRenderApps(true);
    }, 300));
}

// 检查是否接近页面底部
function isNearBottom() {
    const gameList = document.getElementById('game-list');
    const loadMoreButton = document.getElementById('load-more');
    
    // 如果没有更多内容可加载，禁用滚动加载
    if (!loadMoreButton.classList.contains('show')) {
        return false;
    }
    
    return window.innerHeight + window.scrollY >= document.documentElement.scrollHeight - 1000;
}

// 处理初始数据
function processInitialData() {
    filteredApps = [];
    for (const [vendor, apps] of Object.entries(appsData)) {
        for (const [appId, appInfo] of Object.entries(apps)) {
            // 获取开发者信息
            let developerName = vendor;
            if (appInfo.developer && appInfo.developer.name) {
                developerName = appInfo.developer.name;
            }
            filteredApps.push({
                id: appId,
                vendor: developerName,
                ...appInfo
            });
        }
    }
    // 按发布时间排序
    filteredApps.sort((a, b) => new Date(b['Released on']) - new Date(a['Released on']));
}

// 渲染所有筛选器
function renderFilters() {
    renderDayButtons('release');
    renderDayButtons('update');
    renderTagFilters();
    renderVendorFilters();
}

// 渲染日期筛选按钮
function renderDayButtons(type) {
    const dayOptions = [null, 7, 30, 90, 180, 365]; // null 表示"全部"
    const buttonId = type === 'release' ? 'release-day-buttons' : 'update-day-buttons';
    const selectedDays = type === 'release' ? selectedReleaseDays : selectedUpdateDays;
    
    const dayButtons = document.getElementById(buttonId);
    dayButtons.innerHTML = dayOptions.map(days => `
        <button class="filter-button ${days === selectedDays ? 'active' : ''}" data-days="${days || 'all'}">
            ${days ? `${days}天` : '全部'}
        </button>
    `).join('');

    dayButtons.querySelectorAll('.filter-button').forEach(button => {
        button.addEventListener('click', () => {
            const days = button.dataset.days === 'all' ? null : parseInt(button.dataset.days);
            if (type === 'release') {
                selectedReleaseDays = days;
            } else {
                selectedUpdateDays = days;
            }
            dayButtons.querySelectorAll('.filter-button').forEach(b => b.classList.remove('active'));
            button.classList.add('active');
        });
    });
}

// 渲染标签筛选器
function renderTagFilters() {
    const tagSet = new Set();
    filteredApps.forEach(app => {
        (app.genres || []).forEach(tag => tagSet.add(tag));
    });

    const tagGrid = document.getElementById('tag-filter-grid');
    tagGrid.innerHTML = `
        <div class="tag-filter-item" data-tag="all">
            <label class="filter-label">
                <input type="checkbox" id="tag-all">
                <span>全部</span>
            </label>
        </div>
        ${Array.from(tagSet).map(tag => `
            <div class="tag-filter-item" data-tag="${tag}">
                <label class="filter-label">
                    <input type="checkbox" id="tag-${tag}">
                    <span>${tag}</span>
                </label>
            </div>
        `).join('')}
    `;

    const allTagButton = tagGrid.querySelector('[data-tag="all"]');
    const tagButtons = tagGrid.querySelectorAll('.tag-filter-item:not([data-tag="all"])');

    // 修改事件监听器，使用 change 事件
    allTagButton.querySelector('input[type="checkbox"]').addEventListener('change', (e) => {
        allTagButton.classList.toggle('selected', e.target.checked);
        if (e.target.checked) {
            tagButtons.forEach(item => {
                item.classList.remove('selected');
                item.querySelector('input[type="checkbox"]').checked = false;
            });
            selectedTags.clear();
        }
    });

    tagButtons.forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        checkbox.addEventListener('change', (e) => {
            const tag = item.dataset.tag;
            item.classList.toggle('selected', e.target.checked);
            if (e.target.checked) {
                selectedTags.add(tag);
                allTagButton.classList.remove('selected');
                allTagButton.querySelector('input[type="checkbox"]').checked = false;
            } else {
                selectedTags.delete(tag);
                if (selectedTags.size === 0) {
                    allTagButton.classList.add('selected');
                    allTagButton.querySelector('input[type="checkbox"]').checked = true;
                }
            }
        });
    });

    // 默认选中"全部"
    allTagButton.classList.add('selected');
    allTagButton.querySelector('input[type="checkbox"]').checked = true;
}

// 渲染厂商筛选器
function renderVendorFilters() {
    const vendorGrid = document.getElementById('vendor-filter-grid');
    
    // 创建一个包含主要厂商和"其他"的数组
    const filterVendors = [...MAIN_VENDORS, "其他"];
    
    vendorGrid.innerHTML = `
        <div class="tag-filter-item" data-vendor="all">
            <label class="filter-label">
                <input type="checkbox" id="vendor-all">
                <span>全部</span>
            </label>
        </div>
        ${filterVendors.map(vendor => `
            <div class="tag-filter-item ${selectedVendors.has(vendor) ? 'selected' : ''}" data-vendor="${vendor}">
                <label class="filter-label">
                    <input type="checkbox" id="vendor-${vendor}" ${selectedVendors.has(vendor) ? 'checked' : ''}>
                    <span>${vendor}</span>
                </label>
            </div>
        `).join('')}
    `;

    const allVendorButton = vendorGrid.querySelector('[data-vendor="all"]');
    const vendorButtons = vendorGrid.querySelectorAll('.tag-filter-item:not([data-vendor="all"])');

    // 修改事件监听器，使用 change 事件
    allVendorButton.querySelector('input[type="checkbox"]').addEventListener('change', (e) => {
        allVendorButton.classList.toggle('selected', e.target.checked);
        if (e.target.checked) {
            vendorButtons.forEach(item => {
                item.classList.remove('selected');
                item.querySelector('input[type="checkbox"]').checked = false;
            });
            selectedVendors.clear();
        }
    });

    vendorButtons.forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        checkbox.addEventListener('change', (e) => {
            const vendor = item.dataset.vendor;
            item.classList.toggle('selected', e.target.checked);
            if (e.target.checked) {
                selectedVendors.add(vendor);
                allVendorButton.classList.remove('selected');
                allVendorButton.querySelector('input[type="checkbox"]').checked = false;
            } else {
                selectedVendors.delete(vendor);
                if (selectedVendors.size === 0) {
                    allVendorButton.classList.add('selected');
                    allVendorButton.querySelector('input[type="checkbox"]').checked = true;
                }
            }
        });
    });

    // 如果没有选中任何厂商，则选中"全部"
    if (selectedVendors.size === 0) {
        allVendorButton.classList.add('selected');
        allVendorButton.querySelector('input[type="checkbox"]').checked = true;
    } else {
        allVendorButton.classList.remove('selected');
        allVendorButton.querySelector('input[type="checkbox"]').checked = false;
    }
}

// 计算每行可以显示多少个项目
function calculateItemsPerRow() {
    const container = document.querySelector('.app-grid') || document.createElement('div');
    container.style.display = 'flex';
    container.style.flexWrap = 'wrap';
    container.style.gap = '16px';
    container.style.padding = '16px';
    document.body.appendChild(container);

    const testItem = document.createElement('div');
    testItem.className = 'app-item icon-mode';
    container.appendChild(testItem);

    const itemWidth = testItem.offsetWidth + 16; // 包括间距
    const containerWidth = container.offsetWidth;
    
    // 计算每行可以容纳的项目数
    itemsPerRow = Math.floor(containerWidth / itemWidth);
    
    // 清理测试元素
    if (container.parentNode === document.body) {
        document.body.removeChild(container);
    }
}

// 更新统计信息
function updateStats() {
    const filteredResults = filterApps();
    const uniqueVendors = new Set(filteredResults.map(app => app.vendor));
    const statsInfo = document.getElementById('stats-info');
    
    let statsText = '';
    if (hasActiveFilters()) {
        statsText = `筛选结果：${uniqueVendors.size} 个厂商，${filteredResults.length} 款游戏`;
    } else {
        statsText = `总计：${new Set(filteredApps.map(app => app.vendor)).size} 个厂商，${filteredApps.length} 款游戏`;
    }
    
    statsInfo.textContent = statsText;
}

// 检查是否有活动的筛选条件
function hasActiveFilters() {
    return selectedReleaseDays !== null || 
           selectedUpdateDays !== null || 
           selectedTags.size > 0 || 
           selectedVendors.size > 0 || 
           nameFilter !== '';
}

// 筛选应用
function filterApps() {
    return filteredApps.filter(app => {
        // 名称筛选
        if (nameFilter && !app.name.toLowerCase().includes(nameFilter)) {
            return false;
        }

        // 发布时间筛选
        if (selectedReleaseDays) {
            const releaseDate = new Date(app['Released on']);
            const daysDiff = (new Date() - releaseDate) / (1000 * 60 * 60 * 24);
            if (daysDiff > selectedReleaseDays) {
                return false;
            }
        }

        // 更新时间筛选
        if (selectedUpdateDays) {
            const updateDate = new Date(app['Updated on']);
            const daysDiff = (new Date() - updateDate) / (1000 * 60 * 60 * 24);
            if (daysDiff > selectedUpdateDays) {
                return false;
            }
        }

        // 标签筛选
        if (selectedTags.size > 0) {
            const appTags = app.genres || [];
            if (!Array.from(selectedTags).some(tag => appTags.includes(tag))) {
                return false;
            }
        }

        // 厂商筛选（模糊匹配）
        if (selectedVendors.size > 0) {
            const appVendor = (app.developer && app.developer.name) || app.vendor || '';
            
            // 如果没有选中任何厂商，则显示所有应用
            if (selectedVendors.size === 0) {
                return true;
            }

            // 检查是否选中了"其他"选项
            if (selectedVendors.has("其他")) {
                // 检查该应用的开发者是否不在主要厂商列表中
                const isOtherVendor = !MAIN_VENDORS.some(vendor => 
                    vendor.toLowerCase().includes(appVendor.toLowerCase()) || 
                    appVendor.toLowerCase().includes(vendor.toLowerCase())
                );
                
                // 如果只选择了"其他"，则只显示其他厂商的应用
                if (selectedVendors.size === 1) {
                    return isOtherVendor;
                }
                
                // 如果同时选择了"其他"和其他厂商，则显示两者的应用
                return isOtherVendor || Array.from(selectedVendors).some(vendor => 
                    vendor !== "其他" && (
                        vendor.toLowerCase().includes(appVendor.toLowerCase()) || 
                        appVendor.toLowerCase().includes(vendor.toLowerCase())
                    )
                );
            }
            
            // 如果没有选中"其他"，则只显示选中厂商的应用
            return Array.from(selectedVendors).some(vendor => 
                vendor.toLowerCase().includes(appVendor.toLowerCase()) || 
                appVendor.toLowerCase().includes(vendor.toLowerCase())
            );
        }

        return true;
    });
}

// 修改加载和渲染应用的函数
function loadAndRenderApps(reset = false) {
    if (isLoading) return;
    isLoading = true;

    const loadingSpinner = document.getElementById('loading-spinner');
    const loadMoreButton = document.getElementById('load-more');
    const gameList = document.getElementById('game-list');
    
    loadingSpinner.classList.add('active');

    if (reset) {
        gameList.innerHTML = '';
        currentPage = 1;
    }

    const filteredResults = filterApps();
    updateStats();

    if (currentViewMode === 'vendor') {
        // 按厂商分组
        const vendorGroups = {};
        filteredResults.forEach(app => {
            if (!vendorGroups[app.vendor]) {
                vendorGroups[app.vendor] = [];
            }
            vendorGroups[app.vendor].push(app);
        });

        // 按游戏数量排序厂商
        const sortedVendors = Object.entries(vendorGroups)
            .sort(([,a], [,b]) => b.length - a.length);

        // 计算分页
        const TARGET_PAGE_SIZE = 50;
        const MAX_VENDORS_PER_PAGE = 4;
        const MIN_GAMES_FOR_NEW_PAGE = 40;

        const pages = [];
        let currentPageVendors = [];
        let currentPageGames = 0;

        for (const [vendor, apps] of sortedVendors) {
            const vendorGames = apps.length;

            if (vendorGames > TARGET_PAGE_SIZE) {
                if (currentPageVendors.length > 0) {
                    pages.push(currentPageVendors);
                    currentPageVendors = [];
                    currentPageGames = 0;
                }
                pages.push([[vendor, apps]]);
                continue;
            }

            if (currentPageVendors.length >= MAX_VENDORS_PER_PAGE || 
                (currentPageGames >= MIN_GAMES_FOR_NEW_PAGE && currentPageVendors.length > 0)) {
                pages.push(currentPageVendors);
                currentPageVendors = [];
                currentPageGames = 0;
            }

            currentPageVendors.push([vendor, apps]);
            currentPageGames += vendorGames;
        }

        if (currentPageVendors.length > 0) {
            pages.push(currentPageVendors);
        }

        if (currentPage <= pages.length) {
            const pageVendors = pages[currentPage - 1];
            for (const [vendor, apps] of pageVendors) {
                const vendorSection = document.createElement('div');
                vendorSection.className = 'vendor-section';
                const developerUrl = apps[0].developer?.url || `${DEV_PREFIX}${vendor}`;
                const vendorId = `vendor-${vendor.replace(/[^a-zA-Z0-9]/g, '-')}`;
                
                // 检查是否已经存在折叠状态
                const isCollapsed = localStorage.getItem(vendorId) === 'collapsed';
                
                vendorSection.innerHTML = `
                    <div class="vendor-header ${isCollapsed ? 'collapsed' : ''}" data-vendor-id="${vendorId}">
                        <div class="vendor-title">
                            <span class="vendor-name">${vendor}</span>
                            <span class="game-count">（${apps.length}款游戏）</span>
                        </div>
                        <span class="toggle-icon">${isCollapsed ? '▶' : '▼'}</span>
                    </div>
                    <div class="vendor-content ${isCollapsed ? 'collapsed' : ''}" style="max-height: ${isCollapsed ? '0' : '9999px'}">
                        <div class="app-grid">
                            ${apps.map(app => renderAppItem(app)).join('')}
                        </div>
                    </div>
                `;
                
                // 添加折叠/展开事件监听
                const header = vendorSection.querySelector('.vendor-header');
                header.addEventListener('click', () => {
                    const content = vendorSection.querySelector('.vendor-content');
                    const toggleIcon = header.querySelector('.toggle-icon');
                    const isCurrentlyCollapsed = header.classList.contains('collapsed');
                    
                    // 切换折叠状态
                    header.classList.toggle('collapsed');
                    content.classList.toggle('collapsed');
                    
                    // 更新图标
                    toggleIcon.textContent = isCurrentlyCollapsed ? '▼' : '▶';
                    
                    // 设置内容高度
                    if (!isCurrentlyCollapsed) {
                        content.style.maxHeight = '0';
                    } else {
                        content.style.maxHeight = '9999px';
                    }
                    
                    // 保存状态到localStorage
                    localStorage.setItem(vendorId, isCurrentlyCollapsed ? 'expanded' : 'collapsed');
                });
                
                gameList.appendChild(vendorSection);
            }
        }

        loadMoreButton.classList.toggle('show', currentPage < pages.length);
    } else {
        // 图标模式
        const itemsPerPage = itemsPerRow * ROWS_PER_PAGE;
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const pageItems = filteredResults.slice(startIndex, endIndex);

        const container = document.createElement('div');
        container.className = 'app-grid';
        container.innerHTML = pageItems.map(app => renderAppItem(app)).join('');
        gameList.appendChild(container);

        // 更新加载更多按钮的显示状态
        const hasMore = endIndex < filteredResults.length;
        loadMoreButton.classList.toggle('show', hasMore);
        
        // 如果没有更多内容，移除滚动监听
        if (!hasMore) {
            window.removeEventListener('scroll', scrollHandler);
        }
    }

    loadingSpinner.classList.remove('active');
    isLoading = false;
}

// 创建一个防抖的滚动处理函数
const scrollHandler = debounce(() => {
    if (isNearBottom() && !isLoading) {
        currentPage++;
        loadAndRenderApps(false);
    }
}, 200);

// 渲染单个应用项
function renderAppItem(app) {
    const ratingMatch = app.Stat ? app.Stat.match(/([\d.]+)\s*([★☆]+)\s*\((\d+)\)/) : null;
    const rating = ratingMatch ? {
        score: ratingMatch[1],
        stars: ratingMatch[2],
        count: ratingMatch[3]
    } : null;

    const developerUrl = app.developer?.url || `${DEV_PREFIX}${app.vendor}`;
    const developerName = app.vendor;
    const hasFullScreenshots = app.screenshotUrls && app.screenshotUrls.length >= 5;
    
    // 安全处理截图URLs
    const safeScreenshots = app.screenshotUrls ? app.screenshotUrls.map(url => 
        url.replace(/[\\"']/g, '\\$&').replace(/\u0000/g, '\\0')
    ) : [];
    
    return `
        <div class="app-item icon-mode">
            <img class="app-icon ${hasFullScreenshots ? 'has-screenshots' : ''}" 
                 src="${app['Icon URL']}" 
                 alt="${app.name}" 
                 onclick='handleScreenshots(${JSON.stringify(app.id)}, ${JSON.stringify(safeScreenshots)})' />
            <div class="app-info">
                <a href="${APP_PREFIX}${app.id}" target="_blank" class="app-name">${app.name}</a>
                <a href="${developerUrl}" target="_blank" class="developer-name">${developerName}</a>
                ${rating ? `
                    <div class="rating">
                        <span class="rating-stars">${rating.stars}</span>
                        <span class="rating-count">(${rating.count})</span>
                    </div>
                ` : ''}
                <div class="app-dates">
                    <div class="app-date">发布: ${formatDate(app['Released on'])}</div>
                    <div class="app-date">更新: ${formatDate(app['Updated on'])}</div>
                </div>
                ${app.genres && app.genres.length > 0 ? `
                    <div class="game-tags">
                        ${app.genres.map(tag => `<span class="game-tag">${tag}</span>`).join('')}
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

// 处理截图显示的函数
function handleScreenshots(appId, screenshots) {
    if (!screenshots || !Array.isArray(screenshots) || screenshots.length === 0) {
        showToast('没有可用的截图');
        return;
    }
    showScreenshots(appId, screenshots);
}

// 显示截图
function showScreenshots(appId, screenshots) {
    const overlay = document.getElementById('screenshotOverlay');
    const container = overlay.querySelector('.screenshot-container');
    
    container.innerHTML = `
        <div class="screenshot-grid">
            ${screenshots.map(url => `
                <img src="${url}" alt="Screenshot" onerror="this.style.display='none'">
            `).join('')}
        </div>
    `;
    
    // 显示弹窗
    overlay.classList.add('active');
    
    // 阻止滚动传播
    const grid = container.querySelector('.screenshot-grid');
    grid.addEventListener('wheel', (e) => {
        e.stopPropagation();
        
        // 如果内容已经滚动到边缘，则不阻止默认行为
        const isAtStart = grid.scrollLeft === 0;
        const isAtEnd = grid.scrollLeft + grid.clientWidth >= grid.scrollWidth;
        
        if ((isAtStart && e.deltaX < 0) || (isAtEnd && e.deltaX > 0)) {
            return;
        }
        
        e.preventDefault();
        grid.scrollLeft += e.deltaX + e.deltaY;
    });

    // 关闭按钮事件
    const closeScreenshots = () => {
        overlay.classList.remove('active');
        document.removeEventListener('keydown', handleEscKey);
    };

    // ESC键关闭事件
    const handleEscKey = (e) => {
        if (e.key === 'Escape') {
            closeScreenshots();
        }
    };

    // 添加ESC键监听
    document.addEventListener('keydown', handleEscKey);
    
    // 点击关闭按钮
    overlay.querySelector('.screenshot-close').onclick = closeScreenshots;
    
    // 点击背景关闭
    overlay.onclick = (e) => {
        if (e.target === overlay) {
            closeScreenshots();
        }
    };

    // 防止点击截图时关闭
    container.onclick = (e) => {
        e.stopPropagation();
    };
}

// 显示提示消息
function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast-message';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 2000);
}

// 格式化日期
function formatDate(dateStr) {
    if (!dateStr) return '未知';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return '未知';
    return `${date.getFullYear()}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}`;
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 初始化
document.addEventListener('DOMContentLoaded', init); 