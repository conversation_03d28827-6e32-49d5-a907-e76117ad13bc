import sys
import json
import os
from datetime import datetime
import re
import glob
from string import Template

def load_config(config_path):
    with open(config_path, "r", encoding="utf-8") as f:
        return json.load(f)

# 添加主要厂商列表
MAIN_VENDORS = [
    "Voodoo", "HABBY", "Supercell", "Ketchapp", "Rollic",
    "SayGames", "Lion Studios", "SUPERSONIC", "HOMA","BoomBit",
    "Kwalee", "TapNation", "Geisha Tokyo", "Crazy Labs","Supercent",
    "Azur Interactive", "Zplay (Beijing)",
    "Codigames", "JoyPac", "Playgendary", "MADBOX", "Green Panda",
    "tastypill", "King", "Popcore GmbH", "AppQuantum",
    "Gram", "SuperMagic", "Good Job", "Neon Play","Miniclip.com",
    "Capermint","Kolibri Games GmbH","Plarium Global",
    "Ten Square",

]

def load_all_json_files(directory):
    all_data = {}
    # 只读取目录下的json文件，不递归读取子目录
    json_files = glob.glob(os.path.join(directory, "*.json"))
    for json_file in json_files:
        try:
            with open(json_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                filename = os.path.basename(json_file)
                # 使用文件名（不含扩展名）作为key
                key = os.path.splitext(filename)[0]
                
                # 处理新的JSON格式
                if "results" in data:
                    apps_data = {}
                    developer_info = None
                    # 首先找到开发者信息
                    for item in data["results"]:
                        if item.get("wrapperType") == "artist":
                            # 检查开发者名称是否在主要厂商列表中（模糊匹配）
                            dev_name = item.get("artistName", "")
                            matched_vendor = None
                            for vendor in MAIN_VENDORS:
                                if vendor.lower() in dev_name.lower() or dev_name.lower() in vendor.lower():
                                    matched_vendor = vendor
                                    break
                            
                            developer_info = {
                                "name": matched_vendor if matched_vendor else dev_name,
                                "url": item.get("artistLinkUrl", "")
                            }
                            break
                    
                    for item in data["results"]:
                        if "trackId" in item:  # 这是一个应用
                            app_id = str(item["trackId"])
                            rating = float(item.get("averageUserRating", "0"))
                            rating_count = item.get("userRatingCount", "0")
                            rating_stars = "★" * int(rating) + "☆" * (5 - int(rating))
                            
                            # 获取截图URL
                            screenshots = []
                            if "screenshotUrls" in item:
                                screenshots = item["screenshotUrls"]
                            elif "screenshots" in item:
                                screenshots = item["screenshots"]
                            
                            apps_data[app_id] = {
                                "name": item.get("trackName", "未知"),
                                "Released on": item.get("releaseDate", ""),
                                "Updated on": item.get("currentVersionReleaseDate", ""),
                                "Downloads": "未知",
                                "Rating": f"{rating:.2f}",
                                "Rating Count": str(rating_count),
                                "Icon URL": item.get("artworkUrl512", ""),
                                "Stat": f"{rating:.2f} {rating_stars} ({rating_count})",
                                "screenshotUrls": screenshots[:5],  # 只取前5张截图
                                "developer": developer_info,
                                "genres": item.get("genres", [])  # 添加genres字段
                            }
                    all_data[key] = apps_data
                else:
                    all_data[key] = data
        except Exception as e:
            print(f"Error loading {json_file}: {e}")
    return all_data

def parse_date(date_str):
    if not date_str:
        return None
    try:
        # 处理 "2020-05-17" 格式
        if re.match(r"\d{4}-\d{2}-\d{2}$", date_str):
            return datetime.strptime(date_str, "%Y-%m-%d")
        # 处理 "2024-07-08T07:00:00Z" 格式
        elif "T" in date_str:
            return datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%SZ")
        # 处理旧的 "May 14, 2025" 格式
        else:
            return datetime.strptime(date_str, "%b %d, %Y")
    except:
        return None

def date_to_color(release_date):
    if not release_date:
        return "#000000"
    delta = datetime.now() - release_date
    if delta.days > 180:
        return "#000000"
    intensity = 255 - int((delta.days / 180) * 200)
    return f"rgb(0,0,{intensity})"

def get_download_font_size(download_str):
    try:
        num = int(''.join(filter(str.isdigit, download_str or '')))
        if num >= 100000000: return "20px"
        if num >= 10000000:  return "18px"
        if num >= 1000000:   return "16px"
        if num >= 100000:    return "14px"
        if num >= 10000:     return "13px"
        return "12px"
    except:
        return "12px"

def generate_filtered_html(config, history, output_file):
    # 确保输出路径不为空
    if not output_file:
        raise ValueError("输出文件路径不能为空")
    
    # 获取输出文件的目录路径
    output_dir = os.path.dirname(output_file)
    # 如果目录路径为空，使用当前目录
    if not output_dir:
        output_dir = "."
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 使用固定的App Store链接
    dev_prefix = "https://apps.apple.com/developer/"
    app_prefix = "https://apps.apple.com/app/"

    html_parts = [
        '<!DOCTYPE html><html><head><meta charset="utf-8">',
        '<meta name="viewport" content="width=device-width, initial-scale=1">',
        '<title>iOS游戏库</title>',
        '<style>',
        'body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif; margin: 10px; font-size: 15px; }',
        'details { margin-bottom: 20px; }',
        'details summary { padding: 10px; background: #f5f5f5; border-radius: 8px; cursor: pointer; margin-bottom: 10px; }',
        'details summary:hover { background: #e8e8e8; }',
        '.app-grid { display: flex; flex-wrap: wrap; gap: 16px; padding: 16px; min-height: 100px; }',
        '.app-grid.list-mode { display: block; }',
        '.app-grid.icon-mode { display: flex; flex-wrap: wrap; gap: 16px; padding: 16px; }',
        '.app-item { flex: 0 0 160px; padding: 10px; border: 1px solid #ddd; border-radius: 8px; font-size: 14px; background: #fff; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }',
        '.app-item.list-mode { display: grid; grid-template-columns: 80px 200px 1fr 1fr 1fr; gap: 20px; align-items: center; margin-bottom: 10px; flex: none; width: auto; }',
        '.app-item.icon-mode { flex: 0 0 160px; padding: 10px; display: flex; flex-direction: column; gap: 4px; }',
        '.app-item.icon-mode:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.1); }',
        '.app-item.icon-mode .app-icon { width: 80px; height: 80px; margin: 0 auto 8px; }',
        '.app-item.icon-mode a.app-name { font-size: 14px; font-weight: bold; margin-bottom: 2px; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; min-height: 36px; color: #000000; text-decoration: none; }',
        '.app-item.icon-mode a.developer-name { font-size: 12px; color: #666; text-decoration: none; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; min-height: 24px; margin-bottom: 4px; }',
        '.app-item.icon-mode a.developer-name:hover { color: #007AFF; text-decoration: underline; }',
        '.app-item.icon-mode .app-date { font-size: 12px; color: #666; }',
        '.app-item.icon-mode .rating { display: flex; align-items: center; gap: 4px; }',
        '.app-item.icon-mode .game-tags { display: flex; flex-wrap: wrap; gap: 4px; margin-top: 2px; }',
        '.app-item.icon-mode .game-tag { font-size: 11px; padding: 2px 6px; background: #f5f5f5; border-radius: 4px; color: #666; }',
        '.app-item.list-mode .app-icon { width: 80px; height: 80px; margin: 0; }',
        '.app-item.list-mode .app-info { display: flex; flex-direction: column; gap: 4px; }',
        '.app-item.list-mode .app-dates { display: flex; flex-direction: column; gap: 4px; }',
        '.app-item.list-mode .app-stats { display: flex; flex-direction: column; gap: 4px; }',
        '.app-item.list-mode .game-tags { margin: 0; }',
        '.app-item a { font-weight: bold; display: block; margin-bottom: 5px; color: #000000; text-decoration: none; }',
        '.app-item a:hover { color: #007AFF; }',
        '.app-date { font-size: 12px; color: #666; }',
        '@media (max-width: 1200px) {',
        '  .app-item { flex: 0 0 140px; }',
        '  .app-item.icon-mode { flex: 0 0 120px; }',
        '}',
        '@media (max-width: 768px) {',
        '  body { padding: 12px; }',
        '  .app-grid { gap: 10px; padding: 12px; }',
        '  .app-item { padding: 10px; }',
        '  .app-item.icon-mode { flex: 0 0 calc(33.33% - 8px); }',
        '  .filter-content { padding: 16px; }',
        '  .tag-filter-grid { grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)); }',
        '  .tag-filter-item { padding: 6px 8px; font-size: 12px; }',
        '}',
        '@media (max-width: 480px) {',
        '  .app-grid { gap: 8px; padding: 8px; }',
        '  .app-item { padding: 8px; }',
        '  .app-item.icon-mode { flex: 0 0 calc(42%); }',
        '  .filter-toggle { width: 48px; height: 48px; border-radius: 24px; font-size: 20px; }',
        '}',
        '@media (max-width: 360px) {',
        '  .app-item.icon-mode { flex: 0 0 100%; }',
        '}',
        '.vendor-header { font-size: 16px; font-weight: bold; margin-top: 20px; }',
        '.filter-buttons { display: flex; flex-wrap: wrap; gap: 6px; margin-bottom: 10px; }',
        '.filter-button { padding: 6px 12px; font-size: 14px; border: none; border-radius: 6px; background: #007bff; color: white; cursor: pointer; }',
        '.filter-button.active { background: #0056b3; }',
        'h1 { font-size: 24px; margin: 0 0 20px 0; color: #333; }',
        '.vendor-section { margin-bottom: 16px; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }',
        '.vendor-header a { color: #333; text-decoration: none; }',
        '.vendor-header a:hover { color: #007AFF; }',
        '.vendor-header .game-count { font-size: 14px; color: #666; font-weight: normal; }',
        '.app-icon { width: 100%; height: auto; aspect-ratio: 1; border-radius: 22%; margin-bottom: 8px; cursor: pointer; max-width: 120px; margin: 0 auto 8px; display: block; }',
        '.app-icon.has-screenshots { border: 3px solid #34C759; box-shadow: 0 2px 8px rgba(52, 199, 89, 0.2); }',
        '.filter-toggle { position: fixed; bottom: 20px; right: 20px; width: 56px; height: 56px; border-radius: 28px; background: #007AFF; color: white; border: none; font-size: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.2); z-index: 100; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: transform 0.2s; }',
        '.filter-toggle:active { transform: scale(0.95); }',
        '.filter-panel { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; overflow: hidden; }',
        '.filter-panel.active { display: block; }',
        '.filter-content { position: fixed; bottom: 0; left: 0; width: 100%; max-width: 100%; background: white; border-radius: 20px 20px 0 0; padding: 20px; box-shadow: 0 -2px 10px rgba(0,0,0,0.1); max-height: 85vh; overflow-y: auto; -webkit-overflow-scrolling: touch; box-sizing: border-box; }',
        '.filter-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #eee; position: sticky; top: 0; background: white; z-index: 1; }',
        '.filter-title { font-size: 20px; font-weight: 600; color: #333; background: linear-gradient(45deg, #007AFF, #5856D6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }',
        '.filter-close { background: none; border: none; font-size: 24px; color: #666; cursor: pointer; padding: 8px; margin: -8px; }',
        '.filter-section { margin-bottom: 24px; }',
        '.filter-section-title { font-size: 16px; font-weight: 600; color: #333; margin-bottom: 12px; display: flex; align-items: center; }',
        '.filter-section-title::before { content: ""; display: inline-block; width: 4px; height: 16px; background: #007AFF; margin-right: 8px; border-radius: 2px; }',
        '.name-filter-section { margin-bottom: 20px; box-sizing: border-box; width: 100%; }',
        '.name-filter-input { width: 100%; padding: 12px 16px; border: 2px solid #e8e8e8; border-radius: 12px; font-size: 15px; transition: all 0.3s; background: #f8f8f8; box-sizing: border-box; }',
        '.name-filter-input:focus { outline: none; border-color: #007AFF; background: white; box-shadow: 0 2px 8px rgba(0,123,255,0.1); }',
        '.name-filter-input::placeholder { color: #999; }',
        '.tag-filter-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); gap: 8px; padding: 4px; }',
        '.tag-filter-item { display: flex; align-items: center; justify-content: center; padding: 8px 12px; border-radius: 8px; background: #f5f5f5; color: #666; font-size: 14px; cursor: pointer; user-select: none; transition: all 0.2s; border: none; text-align: center; min-height: 36px; }',
        '.tag-filter-item:active { transform: scale(0.98); }',
        '.tag-filter-item.selected { background: #007AFF; color: white; }',
        '.tag-filter-item:hover { background: #e8e8e8; }',
        '.tag-filter-item.selected:hover { background: #0056b3; }',
        '.filter-actions { display: flex; justify-content: space-between; gap: 10px; margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; position: sticky; bottom: 0; background: white; z-index: 2; }',
        '.filter-actions button { padding: 14px 20px; border: none; border-radius: 12px; cursor: pointer; font-size: 16px; flex: 1; background: #007AFF; color: white; font-weight: 600; transition: transform 0.2s; }',
        '.filter-actions button:active { transform: scale(0.98); }',
        '.rating { display: flex; align-items: center; gap: 4px; margin: 4px 0; }',
        '.rating-stars { color: #FF9500; }',
        '.rating-count { color: #666; font-size: 12px; }',
        '.game-tags { display: flex; flex-wrap: wrap; gap: 4px; margin: 6px 0; }',
        '.game-tag { background: #f5f5f5; color: #666; padding: 4px 8px; border-radius: 6px; font-size: 11px; }',
        '.view-mode-buttons { display: flex; gap: 8px; margin: 16px 0; }',
        '.view-mode-button { padding: 10px 16px; font-size: 14px; border: none; border-radius: 10px; background: #f0f0f0; color: #666; cursor: pointer; transition: all 0.2s; }',
        '.view-mode-button:active { transform: scale(0.98); }',
        '.view-mode-button.active { background: #007AFF; color: white; }',
        '.loading-spinner { display: none; text-align: center; padding: 20px; }',
        '.loading-spinner.active { display: block; }',
        '.loading-spinner::after { content: "加载中..."; color: #666; }',
        '.load-more-button { display: none; width: 100%; padding: 15px; margin: 20px 0; background: #f5f5f5; border: none; border-radius: 10px; color: #333; cursor: pointer; font-size: 16px; }',
        '.load-more-button:hover { background: #e8e8e8; }',
        '.load-more-button.show { display: block; }',
        '.screenshot-overlay { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; }',
        '.screenshot-container { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 90%; height: 90%; display: flex; justify-content: center; align-items: center; }',
        '.screenshot-grid { display: flex; gap: 20px; padding: 20px; max-width: 100%; overflow-x: auto; background: rgba(0,0,0,0.5); border-radius: 12px; -webkit-overflow-scrolling: touch; scrollbar-width: thin; scrollbar-color: rgba(255,255,255,0.3) transparent; }',
        '.screenshot-grid::-webkit-scrollbar { height: 8px; }',
        '.screenshot-grid::-webkit-scrollbar-track { background: transparent; }',
        '.screenshot-grid::-webkit-scrollbar-thumb { background-color: rgba(255,255,255,0.3); border-radius: 4px; }',
        '.screenshot-grid img { height: 80vh; width: auto; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }',
        '.screenshot-grid img:hover { transform: scale(1.02); transition: transform 0.2s; }',
        '.screenshot-close { position: fixed; top: 20px; right: 20px; width: 40px; height: 40px; border-radius: 50%; background: rgba(0,0,0,0.5); color: white; border: none; font-size: 24px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s; z-index: 1001; }',
        '.screenshot-close:hover { background: rgba(0,0,0,0.7); transform: scale(1.1); }',
        '.screenshot-overlay.active { display: block; }',
        '.toast-message { position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: rgba(0, 0, 0, 0.8); color: white; padding: 15px 30px; border-radius: 25px; font-size: 16px; opacity: 0; transition: all 0.3s ease; pointer-events: none; z-index: 2000; }',
        '.toast-message.show { opacity: 1; }',
        '.stats-info { font-size: 14px; color: #666; margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 8px; }',
        '.vendor-header { padding: 15px; background: #f8f8f8; cursor: pointer; display: flex; justify-content: space-between; align-items: center; }',
        '.vendor-header:hover { background: #f0f0f0; }',
        '.vendor-header .vendor-title { display: flex; align-items: center; gap: 8px; }',
        '.vendor-header .vendor-name { font-size: 16px; font-weight: 600; color: #333; }',
        '.vendor-header .game-count { font-size: 14px; color: #666; }',
        '.vendor-header .toggle-icon { font-size: 20px; color: #666; transition: transform 0.3s; }',
        '.vendor-header.collapsed .toggle-icon { transform: rotate(-90deg); }',
        '.vendor-content { transition: max-height 0.3s ease-out; overflow: hidden; }',
        '.vendor-content.collapsed { max-height: 0 !important; }',
        '.game-list { min-height: 200px; }',
        '</style>',
        '</head><body>',
        f'<h1>🎮 iOS游戏库 ({datetime.now().strftime("%Y/%m/%d")})</h1>',
        '<div id="view-mode-bar" class="view-mode-buttons" style="margin: 20px 0;">',
        '  <button class="view-mode-button active" data-mode="icon">📱 图标模式</button>',
        '  <button class="view-mode-button" data-mode="vendor">🏢 厂商分类</button>',
        '</div>',
        '<div id="stats-info" class="stats-info"></div>',
        '<div id="game-list"></div>',
        '<div id="loading-spinner" class="loading-spinner"></div>',
        '<button id="load-more" class="load-more-button">加载更多</button>',
        '<div id="filter-toggle" class="filter-toggle">🔍</div>',
        '<div id="filter-panel" class="filter-panel">',
        '  <div class="filter-content">',
        '    <div class="filter-header">',
        '      <div class="filter-title">游戏筛选</div>',
        '      <button class="filter-close">&times;</button>',
        '    </div>',
        '    <div class="filter-section name-filter-section">',
        '      <div class="filter-section-title">游戏名称</div>',
        '      <input type="text" id="name-filter" class="name-filter-input" placeholder="输入游戏名称搜索...">',
        '    </div>',
        '    <div class="filter-section">',
        '      <div class="filter-section-title">上架时间</div>',
        '      <div class="filter-buttons" id="release-day-buttons"></div>',
        '    </div>',
        '    <div class="filter-section">',
        '      <div class="filter-section-title">更新时间</div>',
        '      <div class="filter-buttons" id="update-day-buttons"></div>',
        '    </div>',
        '    <div class="filter-section">',
        '      <div class="filter-section-title">厂商</div>',
        '      <div class="tag-filter-grid" id="vendor-filter-grid"></div>',
        '    </div>',
        '    <div class="filter-section">',
        '      <div class="filter-section-title">游戏类型</div>',
        '      <div class="tag-filter-grid" id="tag-filter-grid"></div>',
        '    </div>',
        '    <div class="filter-actions">',
        '      <button class="filter-apply">应用筛选</button>',
        '    </div>',
        '  </div>',
        '</div>',
        '<div id="screenshotOverlay" class="screenshot-overlay">',
        '<div class="screenshot-container"></div>',
        '<button class="screenshot-close">×</button>',
        '</div>',
    ]

    # 读取JavaScript模板文件,和自己文件名相同的tmpl文件,不要写死    
    script_path = os.path.join(os.path.dirname(__file__), os.path.splitext(os.path.basename(__file__))[0] + ".tmpl")
    with open(script_path, "r", encoding="utf-8") as f:
        js_template = f.read()

    # 将 history 对象和主要厂商列表转换为 JSON 字符串
    history_json = json.dumps(history, ensure_ascii=False, indent=None)
    main_vendors_json = json.dumps(MAIN_VENDORS, ensure_ascii=False, indent=None)
    
    # 确保 JSON 字符串被正确引用
    history_json = history_json.replace('"', '\\"')
    main_vendors_json = main_vendors_json.replace('"', '\\"')

    # 替换模板中的变量
    js_code = js_template.replace('__APPS__', f'"{history_json}"') \
                        .replace('__APP_PREFIX__', f'"{app_prefix}"') \
                        .replace('__DEV_PREFIX__', f'"{dev_prefix}"') \
                        .replace('__MAIN_VENDORS__', f'"{main_vendors_json}"')

    html_parts.append(f"<script>{js_code}</script>")
    html_parts.append("</body></html>")

    with open(output_file, "w", encoding="utf-8") as f:
        f.write('\n'.join(html_parts))

def main():
    import sys
    if len(sys.argv) >= 2:
        config_path = sys.argv[1]
        #把当前系统路径改为 py 文件的目录
        os.chdir(os.path.dirname(sys.argv[1]))
    else:
        script_name = os.path.splitext(os.path.basename(sys.argv[0]))[0]
        # config_path 路径和 py 文件在一个路径下的同名 cfg 文件。
        config_path = f"{script_name}.cfg"
        #把当前系统路径改为 py 文件的目录
        os.chdir(os.path.dirname(sys.argv[0]))

    config = load_config(config_path)
    data_dir = config.get("data_directory", "Developer_AppleMain")
    output_path = config["output_file"]

    history = load_all_json_files(data_dir)
    generate_filtered_html(config, history, output_path)
    print(f"✅ HTML 已生成: {output_path}")

if __name__ == "__main__":
    main() 