# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['ios_game_tracker.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('ios_game_tracker_js.tmpl', '.'),
        ('ios_game_tracker_html.tmpl', '.'),
        ('ios_game_tracker.cfg', '.'),
    ],
    hiddenimports=[
        'requests',
        'urllib3',
        'certifi',
        'json',
        'configparser',
        'datetime',
        'os',
        'sys',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ios_game_tracker',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 改为 False 如果你不想显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)