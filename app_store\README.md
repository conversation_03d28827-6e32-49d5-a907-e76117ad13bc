# iOS游戏追踪器

这是一个合并后的iOS游戏追踪器。

## 功能特点

- ✅ **简化配置**：只需配置开发者ID数组，不再需要完整的iTunes URL
- ✅ **单一程序**：一个程序完成数据获取和HTML生成
- ✅ **模板分离**：HTML和CSS样式不再硬编码在Python中
- ✅ **保留功能**：完全保留了 `fetch_from_itunes_api` 的逻辑
- ✅ **智能缓存**：自动跳过今天已经更新的数据文件

## 文件结构

```
app_store/
├── ios_game_tracker.py          # 主程序文件
├── ios_game_tracker.cfg         # 配置文件
├── ios_game_tracker_html.tmpl   # HTML模板
├── ios_game_tracker_js.tmpl     # JavaScript模板
├── requirements.txt             # Python依赖
└── README.md                    # 说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置文件

编辑 `ios_game_tracker.cfg` 文件：

```json
{
  "developer_ids": [
    1017265454,
    1003169113,
    1009979706
  ],
  "data_directory": "./Developer_Apple/developers",
  "output_file": "./Developer_Apple/recent_apps.html"
}
```

### 配置说明

- `developer_ids`: 开发者ID数组（只需要数字ID，不需要完整URL）
- `data_directory`: JSON数据文件保存目录
- `output_file`: 生成的HTML文件路径

## 使用方法

```bash
python ios_game_tracker.py ios_game_tracker.cfg
```

或者直接运行（会自动使用同名配置文件）：

```bash
python ios_game_tracker.py
```

## 工作流程

1. **读取配置**：从配置文件读取开发者ID列表
2. **获取数据**：使用iTunes API获取每个开发者的应用数据
3. **保存数据**：将数据保存为JSON文件到指定目录
4. **生成HTML**：读取所有JSON文件，生成最终的HTML页面

## 输出示例

### 首次运行（所有文件都需要获取）
```
📱 iOS游戏追踪器
配置文件: ios_game_tracker.cfg
开发者ID数量: 500
数据目录: ./Developer_Apple/developers
输出文件: ./Developer_Apple/recent_apps.html

🔄 第一步：获取开发者数据...
需要更新: 500 个，跳过: 0 个（今天已更新）

[1/500] 正在处理开发者ID: 1017265454
已保存 iTunes 数据: ./Developer_Apple/developers/apple_1017265454.json

数据获取完成！共处理了 500 个开发者ID，跳过了 0 个

🔄 第二步：生成HTML文件...
✅ HTML 已生成: ./Developer_Apple/recent_apps.html

📊 统计信息:
   - 开发者数量: 500
   - 应用总数: 15000
   - 平均每个开发者: 30.0 款应用
```

### 再次运行（今天已更新的文件会被跳过）
```
📱 iOS游戏追踪器
配置文件: ios_game_tracker.cfg
开发者ID数量: 500
数据目录: ./Developer_Apple/developers
输出文件: ./Developer_Apple/recent_apps.html

🔄 第一步：获取开发者数据...
需要更新: 0 个，跳过: 500 个（今天已更新）

数据获取完成！共处理了 0 个开发者ID，跳过了 500 个

🔄 第二步：生成HTML文件...
✅ HTML 已生成: ./Developer_Apple/recent_apps.html

📊 统计信息:
   - 开发者数量: 500
   - 应用总数: 15000
   - 平均每个开发者: 30.0 款应用
```

## 筛选功能说明

### 📅 时间筛选
- **上架时间**：自定义日期范围筛选，默认选中前7天
- **更新时间**：自定义日期范围筛选，默认为全部（无限制）
- **灵活设置**：可以只设置开始日期、只设置结束日期，或设置完整范围
- **一键清除**：提供清除按钮快速重置日期范围

### 🏢 厂商筛选
- **预设厂商**：包含主要游戏厂商列表
- **模糊搜索**：在搜索框中输入厂商名称进行模糊查询
- **智能匹配**：搜索时会同时匹配预设厂商和实际应用数据中的厂商
- **动态更新**：搜索结果会实时更新厂商选择列表

### 🎮 游戏类型筛选
- 支持多选游戏类型标签
- 自动从应用数据中提取所有可用的游戏类型

### 🔍 游戏名称搜索
- 支持实时搜索游戏名称
- 模糊匹配，不区分大小写

## 模板自定义

- 修改 `ios_game_tracker_html.tmpl` 来自定义HTML结构和CSS样式
- 修改 `ios_game_tracker_js.tmpl` 来自定义JavaScript功能

## 最新更新

### v1.4 (最新)
- ✅ **统一日期筛选**：上架时间和更新时间都改为自定义日期范围筛选
- ✅ **智能默认值**：上架时间默认选中前7天，更新时间默认为全部
- ✅ **简化界面**：移除快速选择按钮，统一使用日期选择器

### v1.3
- ✅ **日期范围筛选**：在上架时间筛选中添加自定义日期范围选择功能
- ✅ **厂商模糊搜索**：在厂商筛选中添加搜索输入框，支持模糊查询厂商名称
- ✅ **智能厂商匹配**：搜索时会同时匹配预设厂商和实际应用数据中的厂商

### v1.2
- ✅ **性能优化**：修复文件检查逻辑，避免对已存在的今日文件重复请求API
- ✅ **智能跳过**：程序启动时会先检查所有文件，只处理需要更新的开发者ID

### v1.1
- ✅ **新增1天筛选**：在上架时间和更新时间筛选中添加了1天选项
- ✅ **修复层级问题**：修复了筛选面板滚动时透过看到其他选项的问题

### v1.0
- ✅ 合并 `check_developer.py` 和 `generate_html_ios.py`
- ✅ 简化配置文件格式
- ✅ 移除硬编码HTML模板

## 注意事项

- 程序会自动跳过今天已经更新的数据文件，避免重复请求API
- 确保模板文件存在，否则程序会报错退出
- 建议定期备份生成的数据文件
