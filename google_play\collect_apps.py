import os
import re
import json
import sys
from datetime import datetime

def load_config(config_path):
    with open(config_path, "r", encoding="utf-8") as f:
        return json.load(f)

def load_history(history_path):
    if not os.path.exists(history_path):
        return {}
    with open(history_path, "r", encoding="utf-8") as f:
        return json.load(f)

def save_history(history, history_path):
    os.makedirs(os.path.dirname(history_path), exist_ok=True)  # ✅ 自动创建目录
    with open(history_path, "w", encoding="utf-8") as f:
        json.dump(history, f, ensure_ascii=False, indent=2)
        
def extract_apps_and_packages(html_content, patterns):
    name_pattern = patterns["app_name"]
    package_pattern = patterns["package_name"]

    names = re.findall(name_pattern, html_content, re.DOTALL)
    packages = re.findall(package_pattern, html_content, re.DOTALL)

    apps = []
    for name, package in zip(names, packages):
        apps.append({
            "name": name.strip(),
            "package": package.strip()
        })

    return apps

def process_files(config):
    input_dir = config["input_dir"]
    patterns = config["patterns"]
    today = datetime.today().strftime('%Y-%m-%d')

    extracted_results = {}

    for filename in os.listdir(input_dir):
        if not filename.endswith(".html"):
            continue

        filepath = os.path.join(input_dir, filename)
        with open(filepath, "r", encoding="utf-8") as file:
            content = file.read()

        apps = extract_apps_and_packages(content, patterns)
        
        extracted_results[filename] = {
            "apps": apps,
            "date": today
        }

    return extracted_results

def update_history(extracted_results, history):
    today = datetime.today().strftime('%Y-%m-%d')
    added = {}

    for filename, data in extracted_results.items():
        apps = data["apps"]
        if filename not in history:
            history[filename] = {}

        new_entries = []
        for app in apps:
            package = app["package"]
            if package not in history[filename]:
                history[filename][package] = {
                    "name": app["name"],
                    #"id": app["package"],
                    "入库时间": today
                }
                new_entries.append(app)

        if new_entries:
            added[filename] = new_entries

    return history, added

def show_vendor_app_counts(history):
    print("\n🔹 各个厂商目前的游戏数量统计：")
    for filename, apps_info in history.items():
        vendor = os.path.splitext(filename)[0]
        print(f" - {vendor}: {len(apps_info)}个游戏")

def main():
    if len(sys.argv) >= 2:
        config_path = sys.argv[1]
    else:
        script_name = os.path.splitext(os.path.basename(sys.argv[0]))[0]
        config_path = f"{script_name}.cfg"

    config = load_config(config_path)
    history_path = config["data_file"]

    extracted_results = process_files(config)
    history = load_history(history_path)

    history, added_apps = update_history(extracted_results, history)
    save_history(history, history_path)

    show_vendor_app_counts(history)

    today_str = datetime.today().strftime('%Y-%m-%d')
    if added_apps:
        print(f"\n📥 本次新增入库的 App（{today_str}）：")
        for filename, apps in added_apps.items():
            vendor = os.path.splitext(filename)[0]
            print(f"\n来自: 《{vendor}》")
            for app in apps:
                print(f" - {app['name']}")
    else:
        print(f"\n✅ 本次执行中没有新入库的 App。")

if __name__ == "__main__":
    main()
