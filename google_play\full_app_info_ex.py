import os
import sys
import json
import time
import random
from pathlib import Path
from playwright.sync_api import sync_playwright
import re
import requests
from datetime import datetime

def load_config(config_path):
    with open(config_path, "r", encoding="utf-8") as f:
        return json.load(f)

# 判断是否是打包后的环境
if getattr(sys, 'frozen', False):
    current_dir = sys._MEIPASS
else:
    current_dir = os.path.dirname(os.path.abspath(__file__))
os.environ["PLAYWRIGHT_BROWSERS_PATH"] = os.path.join(current_dir, "ms-playwright")
os.environ["PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD"] = "1"  # 告诉 Playwright 不要下载浏览器

def is_apple_url(url: str) -> bool:
    return "apps.apple.com" in url

def convert_date_format(date_str):
    if not date_str:
        return None
    
    try:
        # 处理 "May 14, 2025" 或 "Apr 14, 2025" 格式
        if "," in date_str:
            # 将月份缩写转换为完整名称
            month_map = {
                'Jan': 'January', 'Feb': 'February', 'Mar': 'March',
                'Apr': 'April', 'May': 'May', 'Jun': 'June',
                'Jul': 'July', 'Aug': 'August', 'Sep': 'September',
                'Oct': 'October', 'Nov': 'November', 'Dec': 'December'
            }
            parts = date_str.split()
            if len(parts) == 3 and parts[0] in month_map:
                date_str = f"{month_map[parts[0]]} {parts[1]} {parts[2]}"
            date_obj = datetime.strptime(date_str, "%B %d, %Y")
            return date_obj.strftime("%Y-%m-%d")
        
        # 处理 "2025-06-05T07:00:00Z" 格式
        elif "T" in date_str:
            date_obj = datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%SZ")
            return date_obj.strftime("%Y-%m-%d")
        
        # 如果已经是 "YYYY-MM-DD" 格式，直接返回
        elif re.match(r"\d{4}-\d{2}-\d{2}", date_str):
            return date_str
            
        # 如果无法识别格式，返回原始值
        return date_str
    except Exception as e:
        print(f"日期转换错误: {date_str}, 错误: {str(e)}")
        # 转换失败时返回原始值
        return date_str

def fetch_from_itunes_api(game_name):
    #game_name 去除 前导的 id
    game_name = game_name.split("id")[1]
    APPLE_URL_TEMPLATE = "https://itunes.apple.com/lookup?id={}"
    #APPLE_URL_TEMPLATE = "https://itunes.apple.com/search?term={}&country=us&entity=software"
    url = APPLE_URL_TEMPLATE.format(game_name)
    try:
        response = requests.get(url)
        if response.status_code != 200:
            return {"error": f"HTTP {response.status_code}"}
        
        data = response.json()
        for item in data.get("results", []):
            # 如果"id" +trackId 和 game_name 完全匹配，则返回数据
            if str(item.get("trackId", "")) == game_name.lower():
                return {
                    "Version": item.get("version", None),  # iTunes API 不提供版本信息
                    "Stat": item.get("averageUserRating", None),     # iTunes API 不提供评分统计
                    "Ratings": item.get("userRatingCount", None),  # iTunes API 不提供评分数量
                    "Updated on": convert_date_format(item.get("currentVersionReleaseDate", None)),
                    "Downloads": None,   # iTunes API 不提供下载量
                    "Released on": convert_date_format(item.get("releaseDate", None)),  # iTunes API 不提供发布日期
                }
        return {"error": "未找到完全匹配的游戏"}
    except Exception as e:
        return {"error": f"解析失败: {str(e)}"}

def extract_google_app_info(url: str):
    with sync_playwright() as p:
        browser = p.chromium.launch(executable_path=os.path.join(
            current_dir,
            "ms-playwright",
            "chromium-1161",
            "chrome-win",
            "chrome.exe"
        ), headless=True)
        
        context = browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                       "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0 Safari/537.36"
        )
        page = context.new_page()
        page.goto(url)

        try:
            page.wait_for_selector("button.VfPpkd-Bz112c-LgbsSe.yHy1rc.eT1oJ.QDwDD.mN1ivc.VxpoF", timeout=5000)
            page.click("button.VfPpkd-Bz112c-LgbsSe.yHy1rc.eT1oJ.QDwDD.mN1ivc.VxpoF")
        except:
            print("click button error.")
            pass

        try:
            page.wait_for_selector('div[jsname="bN97Pc"]', timeout=5000)
            raw_text = page.inner_text('div[jsname="bN97Pc"]')
        except:
            print(f"data error")
            raw_text = ""

        browser.close()

        def extract_field(pattern, text):
            match = re.search(pattern, text)
            return match.group(1).strip() if match else None

        return {
            "Version": extract_field(r"Version\s+([\d.]+)", raw_text),
            "Updated on": convert_date_format(extract_field(r"Updated on\s+([^\n]+)", raw_text)),
            "Downloads": extract_field(r"Downloads\s+([^\n]+)", raw_text),
            "Released on": convert_date_format(extract_field(r"Released on\s+([^\n]+)", raw_text)),
        }

def extract_app_info(url: str):
    if is_apple_url(url):
        return fetch_from_itunes_api(url.split("/")[-1])
    else:
        return extract_google_app_info(url)

def convert_existing_dates(data):
    """转换已有数据中的日期格式"""
    for vendor, games in data.items():
        for game_id, info in games.items():
            # 转换 Updated on
            if "Updated on" in info and info["Updated on"] is not None:
                info["Updated on"] = convert_date_format(info["Updated on"])
            # 转换 Released on
            if "Released on" in info and info["Released on"] is not None:
                info["Released on"] = convert_date_format(info["Released on"])
    return data

def update_app_json_incrementally(json_path: str):
    with open(json_path, "r", encoding="utf-8") as f:
        data = json.load(f)
    
    # 转换已有数据中的日期格式
    data = convert_existing_dates(data)
    # 保存转换后的数据
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

    total_apps = sum(len(apps) for apps in data.values())
    current = 0

    for vendor, games in data.items():
        for game_id, info in games.items():
            current += 1
            print(f"[{current}/{total_apps}] 抓取中：{info.get('name', game_id)}")

            # 检查所有字段是否都为空
            fields_to_check = ["Version", "Stat", "Ratings", "Released on", "Updated on", "Downloads"]
            all_fields_empty = all(not info.get(field) for field in fields_to_check)
            
            # 如果所有字段都为空，则重新收集
            if all_fields_empty:
                print(f"  → 所有字段为空，需要重新收集。")
            else:
                print(f"  → 已有数据，跳过。")
                continue
            
            # 根据厂商名称判断是苹果还是Google Play
            if vendor.startswith("apple_"):  # 苹果应用
                url = f"https://apps.apple.com/us/app/{info['name']}/{game_id}"
            else:  # Google Play应用
                url = f"https://play.google.com/store/apps/details?id={game_id}&hl=en&gl=US"

            try:
                result = extract_app_info(url)

                # 查找缺失字段
                missing_fields = [key for key in fields_to_check if not result.get(key)]

                if missing_fields:
                    print(f"⚠️ 缺失字段 {missing_fields} → {url}")

                info.update(result)

                # 写入 JSON（立即保存）
                with open(json_path, "w", encoding="utf-8") as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)

                print(f"  → 抓取成功：{result}")
            except Exception as e:
                print(f"  ❌ 抓取失败：{info.get('name', game_id)}，原因：{e}")

            # 随机 sleep 防止触发反爬
            time.sleep(random.uniform(0.8, 1.5))

    print("✅ 所有数据已逐条写入完成。")

if __name__ == "__main__":
    if len(sys.argv) >= 2:
        config_path = sys.argv[1]
    else:
        script_name = os.path.splitext(os.path.basename(sys.argv[0]))[0]
        config_path = f"{script_name}.cfg"

    config = load_config(config_path)
    json_file_path = config["data_file"]

    update_app_json_incrementally(json_file_path) 