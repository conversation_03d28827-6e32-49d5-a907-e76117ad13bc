import sys
import json
import os
import requests
from datetime import datetime, date
import glob
from string import Template
import re
import shutil
import argparse
import time
import random
import logging

def get_script_directory():
    """获取脚本文件所在目录，兼容exe打包后的情况"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe
        return os.path.dirname(sys.executable)
    else:
        # 如果是Python脚本
        return os.path.dirname(os.path.abspath(__file__))

# 配置日志
def setup_logging():
    """设置日志配置"""
    # 获取脚本文件所在目录
    script_dir = get_script_directory()
    
    # 创建log目录（相对于脚本文件目录）
    log_dir = os.path.join(script_dir, "log")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 生成带时间戳的日志文件名
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    script_name = "ios_game_tracker"  # 直接指定脚本名称
    log_filename = f"{script_name}_{current_time}.log"
    log_filepath = os.path.join(log_dir, log_filename)
    
    # 配置日志格式
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 创建日志记录器
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    
    # 清除现有的处理器（避免重复）
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建文件处理器（新文件模式）
    file_handler = logging.FileHandler(log_filepath, mode='w', encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter(log_format, date_format)
    file_handler.setFormatter(formatter)
    
    # 添加文件处理器到日志记录器
    logger.addHandler(file_handler)
    
    # 输出日志文件路径信息
    print(f"📝 日志文件: {log_filepath}")
    
    return logger

# 创建日志记录器
logger = setup_logging()

# 保存原始的print函数
original_print = print

# 自定义print函数，同时输出到控制台和日志
def log_print(*args, **kwargs):
    """自定义print函数，同时输出到控制台和日志文件"""
    # 将参数转换为字符串
    message = ' '.join(str(arg) for arg in args)
    
    # 输出到控制台（使用原始print函数）
    original_print(*args, **kwargs)
    
    # 输出到日志（避免重复输出到控制台）
    logger.info(message)

# 替换原有的print函数
print = log_print

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, "r", encoding="utf-8") as f:
        return json.load(f)

def load_developer_ids(script_name):
    """加载开发者ID列表配置文件"""
    # 构建开发者ID配置文件路径 - 相对于脚本文件目录
    script_dir = get_script_directory()
    developers_config_path = os.path.join(script_dir, f"{script_name}_developers.cfg")
    
    try:
        with open(developers_config_path, "r", encoding="utf-8") as f:
            developer_ids = json.load(f)
        
        # 验证数据格式
        if not isinstance(developer_ids, list):
            raise ValueError("开发者ID配置文件必须包含一个数组")
        
        # 确保所有ID都是字符串类型
        developer_ids = [str(dev_id) for dev_id in developer_ids]
        
        print(f"已加载开发者ID配置文件: {developers_config_path}")
        return developer_ids
    except FileNotFoundError:
        print(f"错误：开发者ID配置文件 {developers_config_path} 不存在")
        raise
    except json.JSONDecodeError as e:
        print(f"错误：开发者ID配置文件格式错误 - {e}")
        raise
    except ValueError as e:
        print(f"错误：{e}")
        raise

def fetch_from_itunes_api(dev_id):
    """从 iTunes API 获取开发者数据"""
    url = f"https://itunes.apple.com/lookup?id={dev_id}&entity=software&limit=500"
    try:
        response = requests.get(url)
        if response.status_code != 200:
            print(f"iTunes API 请求失败: HTTP {response.status_code}")
            return None
        
        data = response.json()
        
        # 添加随机延时 150-250ms，避免触发 API 限制
        delay = random.uniform(0.15, 0.25)
        time.sleep(delay)
        
        return data
    except Exception as e:
        print(f"iTunes API 请求异常: {str(e)}")
        return None

def should_update_developer_data(dev_id, temp_dir, specified_date=None):
    """检查是否需要更新开发者数据（基于temp目录中的文件）"""
    return True
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    filename = f"apple_{dev_id}.json"
    filepath = os.path.join(temp_dir, filename)

    # 如果文件不存在，需要更新
    if not os.path.exists(filepath):
        return True

    # 如果文件已存在且是指定日期修改的，则跳过
    file_modification_time = datetime.fromtimestamp(os.path.getmtime(filepath))
    if specified_date:
        # 使用指定的日期进行比较
        if file_modification_time.date() == specified_date:
            return False
    else:
        # 使用今天的日期进行比较
        if file_modification_time.date() == date.today():
            return False

    # 其他情况需要更新
    return True

def save_developer_data(dev_id, data, output_dir):
    """保存开发者数据到 JSON 文件"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    filename = f"apple_{dev_id}.json"
    filepath = os.path.join(output_dir, filename)

    # 保存 JSON 数据
    with open(filepath, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    print(f"已保存 iTunes 数据: {filepath}")

def merge_developer_data(new_data, old_data, current_time):
    """合并新旧开发者数据，添加入库时间和删除时间"""
    if not old_data:
        # 如果没有旧数据，所有新数据都按当前时间入库
        for app_id, app_info in new_data.items():
            app_info['import_time'] = current_time
        return new_data
    
    merged_data = {}
    current_app_ids = set(new_data.keys())
    old_app_ids = set(old_data.keys())
    
    # 处理新增的应用（入库时间设为当前时间）
    for app_id in current_app_ids - old_app_ids:
        app_info = new_data[app_id].copy()
        app_info['import_time'] = current_time
        merged_data[app_id] = app_info
    
    # 处理保留的应用（保持原有入库时间）
    for app_id in current_app_ids & old_app_ids:
        app_info = new_data[app_id].copy()
        # 检查old_data[app_id]是否为字典类型
        if isinstance(old_data[app_id], dict) and 'import_time' in old_data[app_id]:
            app_info['import_time'] = old_data[app_id]['import_time']
        else:
            app_info['import_time'] = current_time
        merged_data[app_id] = app_info
    
    # 处理删除的应用（添加删除时间）
    for app_id in old_app_ids - current_app_ids:
        # 检查old_data[app_id]是否为字典类型
        if isinstance(old_data[app_id], dict):
            app_info = old_data[app_id].copy()
            app_info['delete_time'] = current_time
            merged_data[app_id] = app_info
        else:
            # 如果不是字典类型，跳过这个应用
            print(f"警告：跳过非字典类型的应用数据 {app_id}: {type(old_data[app_id])}")
    
    return merged_data

def merge_developer_data_without_duplicate_import(new_data, old_data, current_time):
    """合并新旧开发者数据，入库时间只在首次入库时设置，其他数据正常更新"""
    if not old_data:
        # 如果没有旧数据，所有新数据都按当前时间入库
        for app_id, app_info in new_data.items():
            # 确保app_info是字典类型
            if isinstance(app_info, dict):
                app_info['import_time'] = current_time
        return new_data
    
    merged_data = {}
    current_app_ids = set(new_data.keys())
    old_app_ids = set(old_data.keys())
    
    # 处理新增的应用（入库时间设为当前时间）
    for app_id in current_app_ids - old_app_ids:
        app_info = new_data[app_id].copy()
        app_info['import_time'] = current_time
        merged_data[app_id] = app_info
    
    # 处理保留的应用（保持原有入库时间，但更新其他数据）
    for app_id in current_app_ids & old_app_ids:
        app_info = new_data[app_id].copy()
        # 检查old_data[app_id]是否为字典类型
        if isinstance(old_data[app_id], dict) and 'import_time' in old_data[app_id]:
            # 如果已有入库时间，保持原有时间（首次入库时间）
            app_info['import_time'] = old_data[app_id]['import_time']
        else:
            # 如果没有入库时间，设置为当前时间
            app_info['import_time'] = current_time
        merged_data[app_id] = app_info
    
    # 处理删除的应用（添加删除时间）
    for app_id in old_app_ids - current_app_ids:
        # 检查old_data[app_id]是否为字典类型
        if isinstance(old_data[app_id], dict):
            app_info = old_data[app_id].copy()
            app_info['delete_time'] = current_time
            # 确保删除的应用也有入库时间
            if 'import_time' not in app_info:
                app_info['import_time'] = current_time
            merged_data[app_id] = app_info
        else:
            # 如果不是字典类型，跳过这个应用
            print(f"警告：跳过非字典类型的应用数据 {app_id}: {type(old_data[app_id])}")
    
    return merged_data

def fetch_all_developers(developer_ids, temp_dir, developers_dir, specified_time=None):
    """批量获取所有开发者数据，先存到temp目录，然后合并到developers目录"""
    total_ids = len(developer_ids)
    print(f"\n开始处理，共有 {total_ids} 个开发者ID需要处理")

    # 如果指定了时间，提取日期部分用于文件检查
    specified_date = None
    if specified_time:
        try:
            specified_date = datetime.strptime(specified_time, "%Y-%m-%d %H:%M:%S").date()
        except ValueError:
            print(f"警告：指定的时间格式错误，使用当前时间: {specified_time}")
            specified_time = None

    # 第一步：检查哪些文件需要下载
    need_download_ids = []
    skipped_download_count = 0

    for dev_id in developer_ids:
        if should_update_developer_data(dev_id, temp_dir, specified_date):
            need_download_ids.append(dev_id)
        else:
            skipped_download_count += 1

    print(f"需要下载: {len(need_download_ids)} 个，跳过下载: {skipped_download_count} 个（今天已下载）")

    # 确保temp目录存在
    if not os.path.exists(temp_dir):
        os.makedirs(temp_dir)

    # 第一步：下载数据到temp目录
    for index, dev_id in enumerate(need_download_ids, 1):
        print(f"\n[{index}/{len(need_download_ids)}] 正在下载开发者ID: {dev_id}")

        data = fetch_from_itunes_api(dev_id)
        if data:
            save_developer_data(dev_id, data, temp_dir)
        else:
            print(f"获取开发者 {dev_id} 数据失败")

    print(f"\n下载完成！共下载了 {len(need_download_ids)} 个开发者ID，跳过了 {skipped_download_count} 个")

    # 第二步：全部入库（包括之前已下载的文件）
    print(f"\n开始入库处理，共有 {total_ids} 个开发者ID需要入库")
    
    for index, dev_id in enumerate(developer_ids, 1):
        print(f"\n[{index}/{total_ids}] 正在入库开发者ID: {dev_id}")
        process_and_merge_data(dev_id, temp_dir, developers_dir, specified_time)

    print(f"\n入库完成！共处理了 {total_ids} 个开发者ID")

def process_and_merge_data(dev_id, temp_dir, developers_dir, specified_time=None):
    """处理并合并开发者数据"""
    # 使用指定的时间或当前时间
    if specified_time:
        current_time = specified_time
    else:
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    filename = f"apple_{dev_id}.json"
    
    # 读取temp目录中的新数据
    temp_filepath = os.path.join(temp_dir, filename)
    developers_filepath = os.path.join(developers_dir, filename)
    
    # 检查temp文件是否存在
    if not os.path.exists(temp_filepath):
        print(f"  跳过：temp目录中不存在文件 {filename}")
        return
    
    try:
        with open(temp_filepath, "r", encoding="utf-8") as f:
            new_data = json.load(f)
    except Exception as e:
        print(f"读取temp数据失败: {e}")
        return
    
    # 读取developers目录中的旧数据
    old_data = None
    if os.path.exists(developers_filepath):
        try:
            with open(developers_filepath, "r", encoding="utf-8") as f:
                old_data = json.load(f)
        except Exception as e:
            print(f"读取developers数据失败: {e}")
            old_data = None
    
    # 处理新的JSON格式（iTunes API原始数据）
    if "results" in new_data:
        # 将原始API数据转换为应用ID为key的格式，保留所有原始信息
        apps_data = {}
        
        for item in new_data["results"]:
            if "trackId" in item:  # 这是一个应用
                app_id = str(item["trackId"])
                # 保存完整的原始数据
                apps_data[app_id] = item.copy()
        
        # 处理旧数据
        old_apps_data = None
        if old_data and "results" in old_data:
            # 旧数据也是原始API格式，需要转换
            old_apps_data = {}
            for item in old_data["results"]:
                if "trackId" in item:  # 这是一个应用
                    app_id = str(item["trackId"])
                    old_apps_data[app_id] = item.copy()
        else:
            # 旧数据已经是处理后的格式
            old_apps_data = old_data
        
        # 合并数据，避免重复入库
        merged_data = merge_developer_data_without_duplicate_import(apps_data, old_apps_data, current_time)
        
        # 保存合并后的数据到developers目录
        if not os.path.exists(developers_dir):
            os.makedirs(developers_dir)
        
        with open(developers_filepath, "w", encoding="utf-8") as f:
            json.dump(merged_data, f, indent=2, ensure_ascii=False)
        
        # 统计信息
        new_count = len(apps_data.keys() - (old_apps_data.keys() if old_apps_data else set()))
        deleted_count = len((old_apps_data.keys() if old_apps_data else set()) - apps_data.keys())
        updated_count = len(apps_data.keys() & (old_apps_data.keys() if old_apps_data else set()))
        
        if new_count > 0 or deleted_count > 0 or updated_count > 0:
            status_msg = []
            if new_count > 0:
                status_msg.append(f"新增: {new_count}")
            if updated_count > 0:
                status_msg.append(f"更新: {updated_count}")
            if deleted_count > 0:
                status_msg.append(f"删除: {deleted_count}")
            print(f"  {', '.join(status_msg)} 个应用")
        else:
            print(f"  无变化")
    else:
        # 处理旧格式数据
        merged_data = merge_developer_data_without_duplicate_import(new_data, old_data, current_time)
        
        if not os.path.exists(developers_dir):
            os.makedirs(developers_dir)
        
        with open(developers_filepath, "w", encoding="utf-8") as f:
            json.dump(merged_data, f, indent=2, ensure_ascii=False)
        
        # 统计信息（旧格式）
        new_count = len(new_data.keys() - (old_data.keys() if old_data else set()))
        deleted_count = len((old_data.keys() if old_data else set()) - new_data.keys())
        updated_count = len(new_data.keys() & (old_data.keys() if old_data else set()))
        
        if new_count > 0 or deleted_count > 0 or updated_count > 0:
            status_msg = []
            if new_count > 0:
                status_msg.append(f"新增: {new_count}")
            if updated_count > 0:
                status_msg.append(f"更新: {updated_count}")
            if deleted_count > 0:
                status_msg.append(f"删除: {deleted_count}")
            print(f"  {', '.join(status_msg)} 个应用")
        else:
            print(f"  无变化")

def convert_full_data_to_display_format(full_data, main_vendor_names=None):
    """将完整的原始数据转换为HTML显示所需的格式"""
    print(f"   convert_full_data_to_display_format: 开始转换数据，共 {len(full_data)} 个开发者")
    display_data = {}
    
    processed_count = 0
    for dev_id, apps_data in full_data.items():
        display_apps = {}
        
        for app_id, app_info in apps_data.items():
            # 不再跳过已删除的应用，让它们也能显示
            # if 'delete_time' in app_info:
            #     continue
                
            # 检查是否为iTunes API原始数据格式
            if isinstance(app_info, dict) and 'trackId' in app_info:
                # 这是iTunes API原始数据
                rating = float(app_info.get("averageUserRating", "0"))
                rating_count = app_info.get("userRatingCount", "0")
                rating_stars = "★" * int(rating) + "☆" * (5 - int(rating))
                
                # 获取截图URL
                screenshots = []
                if "screenshotUrls" in app_info:
                    screenshots = app_info["screenshotUrls"]
                elif "screenshots" in app_info:
                    screenshots = app_info["screenshots"]
                
                # 检查是否有"Games"标签，如果没有则跳过这个应用
                genres = app_info.get("genres", [])
                if "Games" not in genres:
                    continue  # 跳过没有Games标签的应用
                
                # 过滤掉"Games"标签（用于显示）
                filtered_genres = [genre for genre in genres if genre != "Games"]
                
                # 获取开发者信息
                developer_name = app_info.get("artistName", "")
                # 检查开发者名称是否在主要厂商列表中（模糊匹配）
                matched_vendor = None
                if main_vendor_names:
                    for vendor in main_vendor_names:
                        if vendor.lower() in developer_name.lower() or developer_name.lower() in vendor.lower():
                            matched_vendor = vendor
                            break
                
                developer_info = {
                    "name": matched_vendor if matched_vendor else developer_name,
                    "url": app_info.get("artistViewUrl", "")
                }
                
                display_apps[app_id] = {
                    "name": app_info.get("trackName", "未知"),
                    "Released on": app_info.get("releaseDate", ""),
                    "Updated on": app_info.get("currentVersionReleaseDate", ""),
                    "Downloads": "未知",
                    "Rating": f"{rating:.2f}",
                    "Rating Count": str(rating_count),
                    "Icon URL": app_info.get("artworkUrl512", ""),
                    "Stat": f"{rating:.2f} {rating_stars} ({rating_count})",
                    "screenshotUrls": screenshots[:5],  # 只取前5张截图
                    "developer": developer_info,
                    "genres": filtered_genres,
                    "trackViewUrl": app_info.get("trackViewUrl", ""),
                    "import_time": app_info.get("import_time", ""),
                    "delete_time": app_info.get("delete_time", "")
                }
            else:
                # 这是已经处理过的数据格式，直接使用
                display_apps[app_id] = app_info
        
        display_data[dev_id] = display_apps
        processed_count += 1
        if processed_count % 100 == 0:
            print(f"   convert_full_data_to_display_format: 已处理 {processed_count}/{len(full_data)} 个开发者")
    
    print(f"   convert_full_data_to_display_format: 数据转换完成，共处理 {processed_count} 个开发者")
    return display_data

def calculate_vendor_stats(history):
    """计算每个厂商的统计信息：总产品数、500+评分产品数、成功率"""

    vendor_stats = {}

    # 遍历所有开发者数据
    for dev_data in history.values():
        for app_data in dev_data.values():
            if isinstance(app_data, dict):
                
                # 处理iTunes API原始数据格式
                if 'trackId' in app_data:
                    # 这是iTunes API原始数据
                    vendor_name = app_data.get('artistName', '')
                    rating_count = app_data.get('userRatingCount', '0')
                else:
                    # 这是已处理的数据格式
                    vendor_name = app_data.get('developer', {}).get('name', '')
                    rating_count = app_data.get('Rating Count', '0')

                # 转换评分人数为整数
                try:
                    rating_count_int = int(rating_count) if rating_count else 0
                except (ValueError, TypeError):
                    rating_count_int = 0

                if vendor_name:
                    if vendor_name not in vendor_stats:
                        vendor_stats[vendor_name] = {
                            'total_products': 0,
                            'high_rating_products': 0,
                            'total_rating_count': 0,
                            'success_rate': 0.0
                        }

                    # 总产品数+1
                    vendor_stats[vendor_name]['total_products'] += 1

                    # 累加总评分人数
                    vendor_stats[vendor_name]['total_rating_count'] += rating_count_int

                    # 如果评分人数大于等于500，则500+产品数+1
                    if rating_count_int >= 500:
                        vendor_stats[vendor_name]['high_rating_products'] += 1

    # 计算成功率
    for vendor_name, stats in vendor_stats.items():
        if stats['total_products'] > 0:
            stats['success_rate'] = (stats['high_rating_products'] / stats['total_products']) * 100
        else:
            stats['success_rate'] = 0.0

    return vendor_stats

def get_main_vendors(history, top_n=50):
    """根据评分人数500以上的产品数量获取主要厂商列表（前N名）"""
    vendor_stats = calculate_vendor_stats(history)

    # 按评分人数500以上的产品数量排序，取前N名
    sorted_vendors = sorted(vendor_stats.items(), key=lambda x: x[1]['high_rating_products'], reverse=True)
    top_vendors = sorted_vendors[:top_n]

    # 返回格式化的厂商列表：["Voodoo(27)", "HABBY(21)", ...]（数字表示500+评分的产品数量）
    main_vendors_with_scores = [f"{vendor}({stats['high_rating_products']})" for vendor, stats in top_vendors]

    # 同时返回不带分数的厂商名称列表，用于匹配
    main_vendor_names = [vendor for vendor, stats in top_vendors]

    # 返回完整的统计信息
    return main_vendors_with_scores, main_vendor_names, vendor_stats

def load_all_json_files(directory, main_vendor_names=None):
    """加载目录下的所有 JSON 文件，保持原始数据完整性"""
    all_data = {}
    # 只读取目录下的json文件，不递归读取子目录
    json_files = glob.glob(os.path.join(directory, "*.json"))
    for json_file in json_files:
        try:
            with open(json_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                filename = os.path.basename(json_file)
                # 使用文件名（不含扩展名）作为key
                key = os.path.splitext(filename)[0]

                # 处理新的JSON格式（iTunes API原始数据）
                if "results" in data:
                    apps_data = {}
                    for item in data["results"]:
                        if "trackId" in item:  # 这是一个应用
                            app_id = str(item["trackId"])
                            # 保存完整的原始数据
                            apps_data[app_id] = item.copy()
                    all_data[key] = apps_data
                else:
                    # 处理已合并的数据格式（包含入库时间和删除时间）
                    # 不再跳过删除的应用，让它们也能显示
                    all_data[key] = data
        except Exception as e:
            print(f"Error loading {json_file}: {e}")
    return all_data

def parse_date(date_str):
    """解析日期字符串"""
    if not date_str:
        return None
    try:
        # 处理 "2020-05-17" 格式
        if re.match(r"\d{4}-\d{2}-\d{2}$", date_str):
            return datetime.strptime(date_str, "%Y-%m-%d")
        # 处理 "2024-07-08T07:00:00Z" 格式
        elif "T" in date_str:
            return datetime.strptime(date_str, "%Y-%m-%dT%H:%M:%SZ")
        # 处理旧的 "May 14, 2025" 格式
        else:
            return datetime.strptime(date_str, "%b %d, %Y")
    except:
        return None

def date_to_color(release_date):
    """根据发布日期生成颜色"""
    if not release_date:
        return "#000000"
    delta = datetime.now() - release_date
    if delta.days > 180:
        return "#000000"
    intensity = 255 - int((delta.days / 180) * 200)
    return f"rgb(0,0,{intensity})"

def get_download_font_size(download_str):
    """根据下载量获取字体大小"""
    try:
        num = int(''.join(filter(str.isdigit, download_str or '')))
        if num >= 100000000: return "20px"
        if num >= 10000000:  return "18px"
        if num >= 1000000:   return "16px"
        if num >= 100000:    return "14px"
        if num >= 10000:     return "13px"
        return "12px"
    except:
        return "12px"

def fetch_new_apps_for_developers():
    """从iTunes RSS API获取新应用数据，筛选出游戏类应用，提取厂商信息"""
    print("🔄 补充厂商逻辑：获取新应用数据...")
    
    url = "https://itunes.apple.com/us/rss/newapplications/limit=200/json"
    
    try:
        response = requests.get(url)
        if response.status_code != 200:
            print(f"iTunes RSS API 请求失败: HTTP {response.status_code}")
            return []
        
        data = response.json()
        
        # 添加随机延时 150-250ms，避免触发 API 限制
        delay = random.uniform(0.15, 0.25)
        time.sleep(delay)
        
        # 提取entry列表
        if "feed" in data and "entry" in data["feed"]:
            entries = data["feed"]["entry"]
            print(f"   获取到 {len(entries)} 个新应用")
            
            # 筛选游戏类应用（category.attributes.im:id 为 6014）
            game_entries = []
            for entry in entries:
                if "category" in entry and "attributes" in entry["category"]:
                    category_id = entry["category"]["attributes"].get("im:id")
                    if category_id == "6014":  # 游戏分类ID
                        game_entries.append(entry)
            
            print(f"   筛选出 {len(game_entries)} 个游戏应用")
            
            # 提取厂商信息
            developers = []
            for entry in game_entries:
                if "im:artist" in entry and "attributes" in entry["im:artist"]:
                    artist_info = entry["im:artist"]
                    developer_name = artist_info.get("label", "")
                    developer_url = artist_info["attributes"].get("href", "")
                    
                    # 从URL中提取厂商ID
                    developer_id = None
                    if developer_url:
                        # URL格式类似：https://apps.apple.com/cn/developer/xxx/id123456789?uo=2
                        import re
                        match = re.search(r'/id(\d+)', developer_url)
                        if match:
                            developer_id = match.group(1)
                    
                    if developer_id and developer_name:
                        developers.append({
                            "id": developer_id,
                            "name": developer_name,
                            "url": developer_url
                        })
            
            # 去重
            unique_developers = {}
            for dev in developers:
                if dev["id"] not in unique_developers:
                    unique_developers[dev["id"]] = dev
            
            result = list(unique_developers.values())
            print(f"   提取到 {len(result)} 个唯一厂商")
            
            # 输出厂商信息供查看
            print("\n📋 发现的游戏厂商信息：")
            for i, dev in enumerate(result[:], 1):  # 只显示前10个
                print(f"   {i}. ID: {dev['id']}, 名称: {dev['name']}")
                print(f"      地址: {dev['url']}")
            
            
            return result
        else:
            print("   数据格式错误：未找到entry列表")
            return []
            
    except Exception as e:
        print(f"   获取新应用数据失败: {str(e)}")
        return []

def update_developer_config_file(script_name, new_developer_ids):
    """更新开发者配置文件，添加新的厂商ID并去重"""
    # 构建配置文件路径 - 相对于脚本文件目录
    script_dir = get_script_directory()
    config_filename = os.path.join(script_dir, f"{script_name}_developers.cfg")
    
    # 读取现有的开发者ID列表
    existing_ids = []
    try:
        with open(config_filename, "r", encoding="utf-8") as f:
            existing_ids = json.load(f)
        print(f"   读取现有配置文件: {config_filename}")
        print(f"   现有厂商ID数量: {len(existing_ids)}")
    except FileNotFoundError:
        print(f"   配置文件不存在，将创建新文件: {config_filename}")
    except json.JSONDecodeError as e:
        print(f"   配置文件格式错误，将重新创建: {e}")
        existing_ids = []
    
    # 合并并去重，确保所有ID都是字符串类型
    all_ids = list(set(existing_ids + new_developer_ids))
    # 将所有ID转换为字符串类型以确保一致性
    all_ids = [str(dev_id) for dev_id in all_ids]
    all_ids.sort()  # 排序以便于查看
    
    # 计算新增数量（需要将existing_ids也转换为字符串进行比较）
    existing_ids_str = [str(dev_id) for dev_id in existing_ids]
    new_count = len(all_ids) - len(existing_ids_str)
    
    # 保存更新后的配置
    try:
        with open(config_filename, "w", encoding="utf-8") as f:
            json.dump(all_ids, f, indent=2, ensure_ascii=False)
        
        print(f"   配置文件已更新: {config_filename}")
        print(f"   总厂商ID数量: {len(all_ids)}")
        if new_count > 0:
            print(f"   新增厂商ID数量: {new_count}")
        else:
            print(f"   无新增厂商ID")
        
        return all_ids
    except Exception as e:
        print(f"   保存配置文件失败: {e}")
        return existing_ids

def main():
    """主函数"""
    import sys

    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='iOS游戏追踪器')
    parser.add_argument('config_file', nargs='?', help='配置文件路径（可选）')
    parser.add_argument('--time', '-t', help='指定时间，支持格式：YYYY-MM-DD 或 YYYY-MM-DD HH:MM:SS，用于补入库时设置入库时间和删除时间')
    parser.add_argument('--html-only', '-o', action='store_true', help='仅生成HTML文件，跳过数据下载步骤（用于测试）')
    parser.add_argument('--supplement-developers', '-s', action='store_true', help='仅执行补充厂商逻辑：获取新应用数据并提取厂商信息')
    parser.add_argument('--no-supplement', action='store_true', help='跳过自动补充厂商逻辑')
    
    # 解析命令行参数
    args = parser.parse_args()

    # 确定脚本名称（用于配置文件路径）
    if args.config_file:
        script_name = os.path.splitext(os.path.basename(args.config_file))[0]
    else:
        script_name = os.path.splitext(os.path.basename(sys.argv[0]))[0]

    # 如果指定了仅执行补充厂商逻辑，执行后直接返回
    if args.supplement_developers:
        print("🎯 仅执行补充厂商逻辑...")
        new_developers = fetch_new_apps_for_developers()
        if new_developers:
            new_developer_ids = [dev['id'] for dev in new_developers]
            update_developer_config_file(script_name, new_developer_ids)
            print(f"\n✅ 补充厂商逻辑完成，发现 {len(new_developers)} 个新厂商")
        else:
            print("\n❌ 未发现新厂商")
        return

    # 自动执行补充厂商逻辑（除非指定跳过）
    if not args.no_supplement:
        print("🔄 自动执行补充厂商逻辑...")
        new_developers = fetch_new_apps_for_developers()
        if new_developers:
            new_developer_ids = [dev['id'] for dev in new_developers]
            update_developer_config_file(script_name, new_developer_ids)
            print(f"✅ 补充厂商逻辑完成，发现 {len(new_developers)} 个新厂商")
        else:
            print("✅ 补充厂商逻辑完成，未发现新厂商")
        print()

    # 确定配置文件路径
    if args.config_file:
        config_path = args.config_file
        # 把当前系统路径改为配置文件的目录
        os.chdir(os.path.dirname(os.path.abspath(args.config_file)))
        # 从配置文件路径提取脚本名称
        script_name = os.path.splitext(os.path.basename(args.config_file))[0]
    else:
        script_name = os.path.splitext(os.path.basename(sys.argv[0]))[0]
        # config_path 路径和 py 文件在一个路径下的同名 cfg 文件。
        config_path = f"{script_name}.cfg"
        # 把当前系统路径改为 py 文件的目录
        os.chdir(os.path.dirname(os.path.abspath(sys.argv[0])))

    try:
        config = load_config(config_path)
    except FileNotFoundError:
        print(f"错误：配置文件 {config_path} 不存在")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误：配置文件格式错误 - {e}")
        sys.exit(1)

    # 验证配置文件必需字段
    if "data_directory" not in config:
        print("错误：配置文件中缺少 data_directory 字段")
        sys.exit(1)

    if "output_file" not in config:
        print("错误：配置文件中缺少 output_file 字段")
        sys.exit(1)

    # 加载开发者ID列表
    try:
        developer_ids = load_developer_ids(script_name)
    except Exception as e:
        print(f"加载开发者ID失败: {e}")
        sys.exit(1)
    data_dir = config["data_directory"]
    output_path = config["output_file"]
    
    # 创建temp目录路径
    temp_dir = os.path.join(os.path.dirname(data_dir), "temp")

    # 验证开发者ID列表
    if not isinstance(developer_ids, list) or len(developer_ids) == 0:
        print("错误：developer_ids 必须是一个非空的数组")
        sys.exit(1)

    # 处理指定的时间参数
    specified_time = args.time
    if specified_time:
        try:
            # 支持多种时间格式
            if re.match(r"^\d{4}-\d{2}-\d{2}$", specified_time):
                # 只有日期，格式：YYYY-MM-DD，自动添加默认时间 12:00:00
                specified_time = f"{specified_time} 12:00:00"
                print(f"⏰ 检测到日期格式，自动添加默认时间: {specified_time}")
            elif re.match(r"^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$", specified_time):
                # 完整日期时间格式：YYYY-MM-DD HH:MM:SS
                pass
            else:
                raise ValueError("不支持的时间格式")
            
            # 验证时间格式
            datetime.strptime(specified_time, "%Y-%m-%d %H:%M:%S")
            print(f"⏰ 使用指定时间: {specified_time}")
        except ValueError as e:
            print(f"错误：时间格式错误，请使用以下格式之一：")
            print(f"  - 日期格式：YYYY-MM-DD（如：2025-06-20）")
            print(f"  - 完整格式：YYYY-MM-DD HH:MM:SS（如：2025-06-20 14:30:00）")
            print(f"错误详情：{e}")
            sys.exit(1)

    print(f"📱 iOS游戏追踪器")
    print(f"配置文件: {config_path}")
    print(f"开发者ID数量: {len(developer_ids)}")
    print(f"数据目录: {data_dir}")
    print(f"临时目录: {temp_dir}")
    print(f"输出文件: {output_path}")
    if specified_time:
        print(f"指定时间: {specified_time}")
    if args.html_only:
        print(f"🔧 HTML模式: 跳过数据下载，仅生成HTML文件")
    print()

    # 第一步：获取开发者数据（仅在非HTML模式下执行）
    if not args.html_only:
        print("🔄 第一步：获取开发者数据...")
        fetch_all_developers(developer_ids, temp_dir, data_dir, specified_time)
        print()
    else:
        print("⏭️  跳过数据下载步骤...")
        print()

    # 第二步：生成HTML文件
    print("🔄 第二步：生成HTML文件...")

    # 检查数据目录是否存在
    if not os.path.exists(data_dir):
        if args.html_only:
            print(f"❌ 错误：数据目录 {data_dir} 不存在，无法生成HTML文件")
            print(f"   请先运行完整的数据下载流程，或确保数据目录存在")
            sys.exit(1)
        else:
            print(f"   数据目录不存在，将在下载过程中创建")

    # 首先加载完整数据以计算主要厂商
    print("   正在计算主要厂商...")
    full_history = load_all_json_files(data_dir)
    main_vendors_with_scores, main_vendor_names, vendor_stats = get_main_vendors(full_history, 50)
    print(f"   已识别前50主要厂商，500+评分产品数量最多的是: {main_vendors_with_scores[0] if main_vendors_with_scores else '无'}")

    # 将完整数据转换为HTML显示格式
    print("   正在转换数据格式...")
    try:
        display_history = convert_full_data_to_display_format(full_history, main_vendor_names)
        print(f"   数据格式转换完成，共 {len(display_history)} 个开发者")
    except Exception as e:
        print(f"   数据格式转换失败: {e}")
        raise
    
    try:
        print("   正在生成HTML文件...")
        generate_filtered_html(display_history, output_path, main_vendors_with_scores, vendor_stats)
        print(f"✅ HTML 已生成: {output_path}")
    except Exception as e:
        print(f"   HTML生成失败: {e}")
        raise
    print()

    # 统计信息
    total_apps = sum(len(apps) for apps in display_history.values())
    total_developers = len(display_history)
    print(f"📊 统计信息:")
    print(f"   - 开发者数量: {total_developers}")
    print(f"   - 应用总数: {total_apps}")
    print(f"   - 平均每个开发者: {total_apps/total_developers:.1f} 款应用")
    print(f"   - 主要厂商数量: {len(main_vendors_with_scores)}")
    if main_vendors_with_scores:
        print(f"   - 500+评分产品最多的厂商: {main_vendors_with_scores[0]}")
        print(f"   - 500+评分产品最少的主要厂商: {main_vendors_with_scores[-1]}")

def generate_filtered_html(history, output_file, main_vendors_with_scores, vendor_stats):
    """生成过滤后的 HTML 文件"""
    print(f"   generate_filtered_html: 开始生成HTML，输出文件: {output_file}")
    
    # 确保输出路径不为空
    if not output_file:
        raise ValueError("输出文件路径不能为空")

    # 获取输出文件的目录路径
    output_dir = os.path.dirname(output_file)
    # 如果目录路径为空，使用当前目录
    if not output_dir:
        output_dir = "."

    print(f"   generate_filtered_html: 输出目录: {output_dir}")
    
    # 创建输出目录
    try:
        os.makedirs(output_dir, exist_ok=True)
        print(f"   generate_filtered_html: 输出目录创建/确认完成")
    except Exception as e:
        print(f"   generate_filtered_html: 创建输出目录失败: {e}")
        raise

    # 使用固定的App Store链接
    dev_prefix = "https://apps.apple.com/developer/"
    app_prefix = "https://apps.apple.com/us/app/"

    # 读取HTML模板文件
    script_dir = os.path.dirname(__file__)
    html_template_path = os.path.join(script_dir, "ios_game_tracker_html.tmpl")

    try:
        with open(html_template_path, "r", encoding="utf-8") as f:
            html_template = f.read()
    except FileNotFoundError:
        print(f"错误：HTML模板文件 {html_template_path} 不存在")
        raise FileNotFoundError(f"必需的HTML模板文件不存在: {html_template_path}")

    # 读取JavaScript模板文件
    js_template_path = os.path.join(script_dir, "ios_game_tracker_js.tmpl")

    try:
        with open(js_template_path, "r", encoding="utf-8") as f:
            js_template = f.read()
    except FileNotFoundError:
        print(f"错误：JavaScript模板文件 {js_template_path} 不存在")
        raise FileNotFoundError(f"必需的JavaScript模板文件不存在: {js_template_path}")

    # 将 history 对象、主要厂商列表和厂商统计信息转换为 JSON 字符串
    print(f"   generate_filtered_html: 开始转换JSON数据...")
    try:
        history_json = json.dumps(history, ensure_ascii=False, indent=None)
        print(f"   generate_filtered_html: history JSON转换完成，长度: {len(history_json)}")
    except Exception as e:
        print(f"   generate_filtered_html: history JSON转换失败: {e}")
        raise
    
    try:
        main_vendors_json = json.dumps(main_vendors_with_scores, ensure_ascii=False, indent=None)
        print(f"   generate_filtered_html: main_vendors JSON转换完成，长度: {len(main_vendors_json)}")
    except Exception as e:
        print(f"   generate_filtered_html: main_vendors JSON转换失败: {e}")
        raise
    
    try:
        vendor_stats_json = json.dumps(vendor_stats, ensure_ascii=False, indent=None)
        print(f"   generate_filtered_html: vendor_stats JSON转换完成，长度: {len(vendor_stats_json)}")
    except Exception as e:
        print(f"   generate_filtered_html: vendor_stats JSON转换失败: {e}")
        raise

    # 确保 JSON 字符串被正确引用
    print(f"   generate_filtered_html: 开始转义JSON字符串...")
    try:
        history_json = history_json.replace('"', '\\"')
        main_vendors_json = main_vendors_json.replace('"', '\\"')
        vendor_stats_json = vendor_stats_json.replace('"', '\\"')
        print(f"   generate_filtered_html: JSON字符串转义完成")
    except Exception as e:
        print(f"   generate_filtered_html: JSON字符串转义失败: {e}")
        raise

    # 替换JavaScript模板中的变量
    print(f"   generate_filtered_html: 开始替换JavaScript模板变量...")
    try:
        js_code = js_template.replace('__APPS__', f'"{history_json}"') \
                            .replace('__APP_PREFIX__', f'"{app_prefix}"') \
                            .replace('__DEV_PREFIX__', f'"{dev_prefix}"') \
                            .replace('__MAIN_VENDORS__', f'"{main_vendors_json}"') \
                            .replace('__VENDOR_STATS__', f'"{vendor_stats_json}"')
        print(f"   generate_filtered_html: JavaScript模板变量替换完成")
    except Exception as e:
        print(f"   generate_filtered_html: JavaScript模板变量替换失败: {e}")
        raise

    # 替换HTML模板中的变量
    print(f"   generate_filtered_html: 开始替换HTML模板变量...")
    try:
        print(f"   generate_filtered_html: 替换 __TITLE__...")
        html_content = html_template.replace('__TITLE__', 'iOS游戏库')
        print(f"   generate_filtered_html: 替换 __DATE__...")
        html_content = html_content.replace('__DATE__', datetime.now().strftime("%Y/%m/%d"))
        # print(f"   generate_filtered_html: 替换 __JAVASCRIPT__...")
        # html_content = html_content.replace('__JAVASCRIPT__', js_code)
        # print(f"   generate_filtered_html: HTML模板变量替换完成")
    except Exception as e:
        print(f"   generate_filtered_html: HTML模板变量替换失败: {e}")
        import traceback
        print(f"   generate_filtered_html: 详细错误信息: {traceback.format_exc()}")
        raise

    print(f"   generate_filtered_html: 开始分段写入HTML文件...")
    try:
        # 使用 split 方法将模板内容分割成两部分，这比 replace 内存效率高得多
        parts = html_content.split('__JAVASCRIPT__', 1)

        with open(output_file, "w", encoding="utf-8") as f:
            # 写入占位符之前的部分
            f.write(parts[0])
            
            # 直接写入巨大的 js_code 字符串
            print(f"   generate_filtered_html: 正在写入 __JAVASCRIPT__ 内容...")
            f.write(js_code)
            
            # 如果占位符后面还有内容，则写入剩余部分
            if len(parts) > 1:
                f.write(parts[1])
                
        print(f"   generate_filtered_html: HTML文件写入完成")
    except Exception as e:
        print(f"   generate_filtered_html: HTML文件写入失败: {e}")
        # traceback 已经在您的原代码中，所以这里不算是引入新库
        import traceback
        print(f"   generate_filtered_html: 详细错误信息: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    main()
