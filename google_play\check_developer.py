import sys
from playwright.sync_api import sync_playwright
import time
from datetime import datetime,date
import os
import json
import random
import requests


def load_config(config_path):
    with open(config_path, "r", encoding="utf-8") as f:
        return json.load(f)

import urllib.parse
import re

# 判断是否是打包后的环境
if getattr(sys, 'frozen', False):
    current_dir = sys._MEIPASS
else:
    current_dir = os.path.dirname(os.path.abspath(__file__))
os.environ["PLAYWRIGHT_BROWSERS_PATH"] = os.path.join(current_dir, "ms-playwright")
os.environ["PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD"] = "1"  # 告诉 Playwright 不要下载浏览器

def fetch_from_itunes_api(dev_id):
    url = f"https://itunes.apple.com/lookup?id={dev_id}&entity=software&limit=500"
    try:
        response = requests.get(url)
        if response.status_code != 200:
            print(f"iTunes API 请求失败: HTTP {response.status_code}")
            return None
        
        data = response.json()
        return data
    except Exception as e:
        print(f"iTunes API 请求异常: {str(e)}")
        return None

def scrape_page(url, output_dir):
    # 检查是否是 iTunes API URL
    if url.startswith("https://itunes.apple.com/lookup"):
        try:
            # 从 URL 中提取开发者 ID
            dev_id = url.split("id=")[1].split("&")[0]
            filename = f"apple_{dev_id}.json"
            filepath = os.path.join(output_dir, filename)
            
            # 如果文件已存在且是今天修改的，则跳过
            if os.path.exists(filepath):
                file_modification_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                if file_modification_time.date() == date.today():
                    print(f"文件已存在且是今天修改的，跳过: {filepath}")
                    return
            
            data = fetch_from_itunes_api(dev_id)
            if data:
                # 保存 JSON 数据
                with open(filepath, "w", encoding="utf-8") as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                print(f"已保存 iTunes 数据: {filepath}")
                return
        except Exception as e:
            print(f"处理 iTunes URL 时出错: {str(e)}")
            return

    # 根据网址判断平台和提取厂商名逻辑
    if "apps.apple.com" in url:
        # 苹果网址格式: https://apps.apple.com/us/developer/<开发者名>/id123456
        match = re.search(r'/developer/(.+)/id(\d+)', url)
        if match:
            dev_name = match.group(1)
            dev_id = match.group(2)
            # 转码苹果的厂商名（可能包含汉字）
            encoded_name = urllib.parse.unquote(dev_name)
            filename = f"apple_{encoded_name}_{dev_id}.html"
        else:
            raise ValueError("苹果网址格式不匹配，无法解析厂商名")
    elif "play.google.com" in url:
        # Google Play网址格式: https://play.google.com/store/apps/developer?id=<开发者名>
        match = re.search(r'developer\?id=([^&]+)', url)
        if match:
            dev_name = match.group(1).replace('+', '+')
            filename = f"{dev_name}.html"
        else:
            raise ValueError("Google Play网址格式不匹配，无法解析厂商名")
    else:
        raise ValueError("未知平台的网址，无法处理")

    filepath = os.path.join(output_dir, filename)

    # 如果文件已存在且是今天修改的，则跳过
    if os.path.exists(filepath):
        file_modification_time = datetime.fromtimestamp(os.path.getmtime(filepath))
        if file_modification_time.date() == date.today():
            print(f"文件已存在且是今天修改的，跳过: {filepath}")
            return

    # 只有在需要抓取时才启动浏览器
    with sync_playwright() as p:
        browser = p.chromium.launch(executable_path=os.path.join(
                current_dir,
                "ms-playwright",
                "chromium-1161",
                "chrome-win",
                "chrome.exe"
            ), headless=True)       
         
        context = browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent=(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0 Safari/537.36"
            )
        )
        page = context.new_page()

        if url.startswith('#') or not url:
            return

        print(f"正在加载: {url}")
        page.goto(url, timeout=120000)
        time.sleep(random.uniform(1, 2))

        previous_height = 0
        retry = 0
        while retry < 5:
            page.evaluate("window.scrollBy(0, 800);")
            time.sleep(random.uniform(0.5, 1.5))
            current_height = page.evaluate("document.body.scrollHeight")
            if current_height == previous_height:
                retry += 1
            else:
                retry = 0
                previous_height = current_height

        time.sleep(5)  # 等待加载

        # 存储文件
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(page.content())

        browser.close()
        print(f"已保存: {filepath}")

# 批量加载开发者网址
def batch_scrape(developers, output_dir):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 过滤掉空行和注释行
    valid_urls = [url.strip() for url in developers if url.strip() and not url.strip().startswith("#")]
    total_urls = len(valid_urls)
    
    print(f"\n开始处理，共有 {total_urls} 个URL需要处理")
    
    for index, url in enumerate(valid_urls, 1):
        print(f"\n[{index}/{total_urls}] 正在处理第 {index} 个URL，共 {total_urls} 个")
        scrape_page(url, output_dir)
    
    print(f"\n处理完成！共处理了 {total_urls} 个URL")

if __name__ == "__main__":
    if len(sys.argv) >= 2:
        config_path = sys.argv[1]
    else:
        script_name = os.path.splitext(os.path.basename(sys.argv[0]))[0]
        config_path = f"{script_name}.cfg"

    config = load_config(config_path)

    if "developers" not in config or "output_dir" not in config:
        print("错误：配置文件中缺少 developers 或 output_dir 字段。")
        sys.exit(1)

    developers = config["developers"]
    output_dir = config["output_dir"]

    batch_scrape(developers, output_dir)